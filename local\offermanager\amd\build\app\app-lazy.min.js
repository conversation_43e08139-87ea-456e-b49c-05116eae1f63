define("local_offermanager/app/app-lazy",function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function er(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const nt={}.NODE_ENV!=="production"?Object.freeze({}):{},xn={}.NODE_ENV!=="production"?Object.freeze([]):[],Ot=()=>{},Hg=()=>!1,so=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),oi=e=>e.startsWith("onUpdate:"),pt=Object.assign,Qa=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},qg=Object.prototype.hasOwnProperty,Ze=(e,t)=>qg.call(e,t),ge=Array.isArray,Yr=e=>ro(e)==="[object Map]",Sn=e=>ro(e)==="[object Set]",Rc=e=>ro(e)==="[object Date]",Se=e=>typeof e=="function",ft=e=>typeof e=="string",As=e=>typeof e=="symbol",Xe=e=>e!==null&&typeof e=="object",Za=e=>(Xe(e)||Se(e))&&Se(e.then)&&Se(e.catch),Vc=Object.prototype.toString,ro=e=>Vc.call(e),Ja=e=>ro(e).slice(8,-1),Fc=e=>ro(e)==="[object Object]",Xa=e=>ft(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,no=er(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),zg=er("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),ii=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Wg=/-(\w)/g,Kt=ii(e=>e.replace(Wg,(t,s)=>s?s.toUpperCase():"")),Gg=/\B([A-Z])/g,Sr=ii(e=>e.replace(Gg,"-$1").toLowerCase()),Qr=ii(e=>e.charAt(0).toUpperCase()+e.slice(1)),Zr=ii(e=>e?`on${Qr(e)}`:""),Or=(e,t)=>!Object.is(e,t),On=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ai=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},li=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Kg=e=>{const t=ft(e)?Number(e):NaN;return isNaN(t)?e:t};let Uc;const oo=()=>Uc||(Uc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ls(e){if(ge(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=ft(i)?Jg(i):ls(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(ft(e)||Xe(e))return e}const Yg=/;(?![^(]*\))/g,Qg=/:([^]+)/,Zg=/\/\*[^]*?\*\//g;function Jg(e){const t={};return e.replace(Zg,"").split(Yg).forEach(s=>{if(s){const i=s.split(Qg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function pe(e){let t="";if(ft(e))t=e;else if(ge(e))for(let s=0;s<e.length;s++){const i=pe(e[s]);i&&(t+=i+" ")}else if(Xe(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Xg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",ev="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",tv="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",sv=er(Xg),rv=er(ev),nv=er(tv),ov=er("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Lc(e){return!!e||e===""}function iv(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=io(e[i],t[i]);return s}function io(e,t){if(e===t)return!0;let s=Rc(e),i=Rc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=As(e),i=As(t),s||i)return e===t;if(s=ge(e),i=ge(t),s||i)return s&&i?iv(e,t):!1;if(s=Xe(e),i=Xe(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!io(e[u],t[u]))return!1}}return String(e)===String(t)}function el(e,t){return e.findIndex(s=>io(s,t))}const Bc=e=>!!(e&&e.__v_isRef===!0),W=e=>ft(e)?e:e==null?"":ge(e)||Xe(e)&&(e.toString===Vc||!Se(e.toString))?Bc(e)?W(e.value):JSON.stringify(e,$c,2):String(e),$c=(e,t)=>Bc(t)?$c(e,t.value):Yr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[tl(i,a)+" =>"]=n,s),{})}:Sn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>tl(s))}:As(t)?tl(t):Xe(t)&&!ge(t)&&!Fc(t)?String(t):t,tl=(e,t="")=>{var s;return As(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let us;class jc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=us,!t&&us&&(this.index=(us.scopes||(us.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=us;try{return us=this,t()}finally{us=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){us=this}off(){us=this.parent}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function av(e){return new jc(e)}function lv(){return us}let ot;const sl=new WeakSet;class Hc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,us&&us.active&&us.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,sl.has(this)&&(sl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||zc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Qc(this),Wc(this);const t=ot,s=Ms;ot=this,Ms=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&ot!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),Gc(this),ot=t,Ms=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)il(t);this.deps=this.depsTail=void 0,Qc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?sl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ol(this)&&this.run()}get dirty(){return ol(this)}}let qc=0,ao,lo;function zc(e,t=!1){if(e.flags|=8,t){e.next=lo,lo=e;return}e.next=ao,ao=e}function rl(){qc++}function nl(){if(--qc>0)return;if(lo){let t=lo;for(lo=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;ao;){let t=ao;for(ao=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function Wc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Gc(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),il(i),uv(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function ol(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Kc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Kc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===uo))return;e.globalVersion=uo;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!ol(e)){e.flags&=-3;return}const s=ot,i=Ms;ot=e,Ms=!0;try{Wc(e);const n=e.fn(e._value);(t.version===0||Or(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{ot=s,Ms=i,Gc(e),e.flags&=-3}}function il(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)il(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function uv(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ms=!0;const Yc=[];function tr(){Yc.push(Ms),Ms=!1}function sr(){const e=Yc.pop();Ms=e===void 0?!0:e}function Qc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ot;ot=void 0;try{t()}finally{ot=s}}}let uo=0;class cv{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class al{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!ot||!Ms||ot===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ot)s=this.activeLink=new cv(ot,this),ot.deps?(s.prevDep=ot.depsTail,ot.depsTail.nextDep=s,ot.depsTail=s):ot.deps=ot.depsTail=s,Zc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=ot.depsTail,s.nextDep=void 0,ot.depsTail.nextDep=s,ot.depsTail=s,ot.deps===s&&(ot.deps=i)}return{}.NODE_ENV!=="production"&&ot.onTrack&&ot.onTrack(pt({effect:ot},t)),s}trigger(t){this.version++,uo++,this.notify(t)}notify(t){rl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(pt({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{nl()}}}function Zc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Zc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ll=new WeakMap,Jr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),ul=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),co=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function It(e,t,s){if(Ms&&ot){let i=ll.get(e);i||ll.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new al),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function qs(e,t,s,i,n,a){const u=ll.get(e);if(!u){uo++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):h.trigger())};if(rl(),t==="clear")u.forEach(c);else{const h=ge(e),m=h&&Xa(s);if(h&&s==="length"){const p=Number(i);u.forEach((v,w)=>{(w==="length"||w===co||!As(w)&&w>=p)&&c(v)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),m&&c(u.get(co)),t){case"add":h?m&&c(u.get("length")):(c(u.get(Jr)),Yr(e)&&c(u.get(ul)));break;case"delete":h||(c(u.get(Jr)),Yr(e)&&c(u.get(ul)));break;case"set":Yr(e)&&c(u.get(Jr));break}}nl()}function In(e){const t=Te(e);return t===e?t:(It(t,"iterate",co),Yt(e)?t:t.map(Ht))}function ui(e){return It(e=Te(e),"iterate",co),e}const dv={__proto__:null,[Symbol.iterator](){return cl(this,Symbol.iterator,Ht)},concat(...e){return In(this).concat(...e.map(t=>ge(t)?In(t):t))},entries(){return cl(this,"entries",e=>(e[1]=Ht(e[1]),e))},every(e,t){return rr(this,"every",e,t,void 0,arguments)},filter(e,t){return rr(this,"filter",e,t,s=>s.map(Ht),arguments)},find(e,t){return rr(this,"find",e,t,Ht,arguments)},findIndex(e,t){return rr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return rr(this,"findLast",e,t,Ht,arguments)},findLastIndex(e,t){return rr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return rr(this,"forEach",e,t,void 0,arguments)},includes(...e){return dl(this,"includes",e)},indexOf(...e){return dl(this,"indexOf",e)},join(e){return In(this).join(e)},lastIndexOf(...e){return dl(this,"lastIndexOf",e)},map(e,t){return rr(this,"map",e,t,void 0,arguments)},pop(){return fo(this,"pop")},push(...e){return fo(this,"push",e)},reduce(e,...t){return Jc(this,"reduce",e,t)},reduceRight(e,...t){return Jc(this,"reduceRight",e,t)},shift(){return fo(this,"shift")},some(e,t){return rr(this,"some",e,t,void 0,arguments)},splice(...e){return fo(this,"splice",e)},toReversed(){return In(this).toReversed()},toSorted(e){return In(this).toSorted(e)},toSpliced(...e){return In(this).toSpliced(...e)},unshift(...e){return fo(this,"unshift",e)},values(){return cl(this,"values",Ht)}};function cl(e,t,s){const i=ui(e),n=i[t]();return i!==e&&!Yt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const fv=Array.prototype;function rr(e,t,s,i,n,a){const u=ui(e),c=u!==e&&!Yt(e),h=u[t];if(h!==fv[t]){const v=h.apply(e,a);return c?Ht(v):v}let m=s;u!==e&&(c?m=function(v,w){return s.call(this,Ht(v),w,e)}:s.length>2&&(m=function(v,w){return s.call(this,v,w,e)}));const p=h.call(u,m,i);return c&&n?n(p):p}function Jc(e,t,s,i){const n=ui(e);let a=s;return n!==e&&(Yt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ht(c),h,e)}),n[t](a,...i)}function dl(e,t,s){const i=Te(e);It(i,"iterate",co);const n=i[t](...s);return(n===-1||n===!1)&&mi(s[0])?(s[0]=Te(s[0]),i[t](...s)):n}function fo(e,t,s=[]){tr(),rl();const i=Te(e)[t].apply(e,s);return nl(),sr(),i}const hv=er("__proto__,__v_isRef,__isVue"),Xc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(As));function pv(e){As(e)||(e=String(e));const t=Te(this);return It(t,"has",e),t.hasOwnProperty(e)}class ed{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?ad:id:a?od:nd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=ge(t);if(!n){let h;if(u&&(h=dv[s]))return h;if(s==="hasOwnProperty")return pv}const c=Reflect.get(t,s,Dt(t)?t:i);return(As(s)?Xc.has(s):hv(s))||(n||It(t,"get",s),a)?c:Dt(c)?u&&Xa(s)?c:c.value:Xe(c)?n?ud(c):hi(c):c}}class td extends ed{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const h=nr(a);if(!Yt(i)&&!nr(i)&&(a=Te(a),i=Te(i)),!ge(t)&&Dt(a)&&!Dt(i))return h?!1:(a.value=i,!0)}const u=ge(t)&&Xa(s)?Number(s)<t.length:Ze(t,s),c=Reflect.set(t,s,i,Dt(t)?t:n);return t===Te(n)&&(u?Or(i,a)&&qs(t,"set",s,i,a):qs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ze(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&qs(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!As(s)||!Xc.has(s))&&It(t,"has",s),i}ownKeys(t){return It(t,"iterate",ge(t)?"length":Jr),Reflect.ownKeys(t)}}class sd extends ed{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const mv=new td,gv=new sd,vv=new td(!0),_v=new sd(!0),fl=e=>e,ci=e=>Reflect.getPrototypeOf(e);function yv(e,t,s){return function(...i){const n=this.__v_raw,a=Te(n),u=Yr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,m=n[e](...i),p=s?fl:t?pl:Ht;return!t&&It(a,"iterate",h?ul:Jr),{next(){const{value:v,done:w}=m.next();return w?{value:v,done:w}:{value:c?[p(v[0]),p(v[1])]:p(v),done:w}},[Symbol.iterator](){return this}}}}function di(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Qr(e)} operation ${s}failed: target is readonly.`,Te(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function bv(e,t){const s={get(n){const a=this.__v_raw,u=Te(a),c=Te(n);e||(Or(n,c)&&It(u,"get",n),It(u,"get",c));const{has:h}=ci(u),m=t?fl:e?pl:Ht;if(h.call(u,n))return m(a.get(n));if(h.call(u,c))return m(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&It(Te(n),"iterate",Jr),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Te(a),c=Te(n);return e||(Or(n,c)&&It(u,"has",n),It(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,h=Te(c),m=t?fl:e?pl:Ht;return!e&&It(h,"iterate",Jr),c.forEach((p,v)=>n.call(a,m(p),m(v),u))}};return pt(s,e?{add:di("add"),set:di("set"),delete:di("delete"),clear:di("clear")}:{add(n){!t&&!Yt(n)&&!nr(n)&&(n=Te(n));const a=Te(this);return ci(a).has.call(a,n)||(a.add(n),qs(a,"add",n,n)),this},set(n,a){!t&&!Yt(a)&&!nr(a)&&(a=Te(a));const u=Te(this),{has:c,get:h}=ci(u);let m=c.call(u,n);m?{}.NODE_ENV!=="production"&&rd(u,c,n):(n=Te(n),m=c.call(u,n));const p=h.call(u,n);return u.set(n,a),m?Or(a,p)&&qs(u,"set",n,a,p):qs(u,"add",n,a),this},delete(n){const a=Te(this),{has:u,get:c}=ci(a);let h=u.call(a,n);h?{}.NODE_ENV!=="production"&&rd(a,u,n):(n=Te(n),h=u.call(a,n));const m=c?c.call(a,n):void 0,p=a.delete(n);return h&&qs(a,"delete",n,void 0,m),p},clear(){const n=Te(this),a=n.size!==0,u={}.NODE_ENV!=="production"?Yr(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&qs(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=yv(n,e,t)}),s}function fi(e,t){const s=bv(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(Ze(s,n)&&n in i?s:i,n,a)}const wv={get:fi(!1,!1)},Ev={get:fi(!1,!0)},Cv={get:fi(!0,!1)},Dv={get:fi(!0,!0)};function rd(e,t,s){const i=Te(s);if(i!==s&&t.call(e,i)){const n=Ja(e);Hs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const nd=new WeakMap,od=new WeakMap,id=new WeakMap,ad=new WeakMap;function xv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Sv(e){return e.__v_skip||!Object.isExtensible(e)?0:xv(Ja(e))}function hi(e){return nr(e)?e:pi(e,!1,mv,wv,nd)}function ld(e){return pi(e,!1,vv,Ev,od)}function ud(e){return pi(e,!0,gv,Cv,id)}function zs(e){return pi(e,!0,_v,Dv,ad)}function pi(e,t,s,i,n){if(!Xe(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=n.get(e);if(a)return a;const u=Sv(e);if(u===0)return e;const c=new Proxy(e,u===2?i:s);return n.set(e,c),c}function Xr(e){return nr(e)?Xr(e.__v_raw):!!(e&&e.__v_isReactive)}function nr(e){return!!(e&&e.__v_isReadonly)}function Yt(e){return!!(e&&e.__v_isShallow)}function mi(e){return e?!!e.__v_raw:!1}function Te(e){const t=e&&e.__v_raw;return t?Te(t):e}function hl(e){return!Ze(e,"__v_skip")&&Object.isExtensible(e)&&ai(e,"__v_skip",!0),e}const Ht=e=>Xe(e)?hi(e):e,pl=e=>Xe(e)?ud(e):e;function Dt(e){return e?e.__v_isRef===!0:!1}function cd(e){return dd(e,!1)}function Ov(e){return dd(e,!0)}function dd(e,t){return Dt(e)?e:new Iv(e,t)}class Iv{constructor(t,s){this.dep=new al,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Te(t),this._value=s?t:Ht(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Yt(t)||nr(t);t=i?t:Te(t),Or(t,s)&&(this._rawValue=t,this._value=i?t:Ht(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Ir(e){return Dt(e)?e.value:e}const Nv={get:(e,t,s)=>t==="__v_raw"?e:Ir(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return Dt(n)&&!Dt(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function fd(e){return Xr(e)?e:new Proxy(e,Nv)}class Tv{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new al(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=uo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&ot!==this)return zc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return Kc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function Av(e,t,s=!1){let i,n;Se(e)?i=e:(i=e.get,n=e.set);const a=new Tv(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const gi={},vi=new WeakMap;let en;function Mv(e,t=!1,s=en){if(s){let i=vi.get(s);i||vi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function Pv(e,t,s=nt){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:h}=s,m=Q=>{(s.onWarn||Hs)("Invalid watch source: ",Q,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=Q=>n?Q:Yt(Q)||n===!1||n===0?or(Q,1):or(Q);let v,w,D,k,U=!1,te=!1;if(Dt(e)?(w=()=>e.value,U=Yt(e)):Xr(e)?(w=()=>p(e),U=!0):ge(e)?(te=!0,U=e.some(Q=>Xr(Q)||Yt(Q)),w=()=>e.map(Q=>{if(Dt(Q))return Q.value;if(Xr(Q))return p(Q);if(Se(Q))return h?h(Q,2):Q();({}).NODE_ENV!=="production"&&m(Q)})):Se(e)?t?w=h?()=>h(e,2):e:w=()=>{if(D){tr();try{D()}finally{sr()}}const Q=en;en=v;try{return h?h(e,3,[k]):e(k)}finally{en=Q}}:(w=Ot,{}.NODE_ENV!=="production"&&m(e)),t&&n){const Q=w,he=n===!0?1/0:n;w=()=>or(Q(),he)}const T=lv(),se=()=>{v.stop(),T&&T.active&&Qa(T.effects,v)};if(a&&t){const Q=t;t=(...he)=>{Q(...he),se()}}let G=te?new Array(e.length).fill(gi):gi;const we=Q=>{if(!(!(v.flags&1)||!v.dirty&&!Q))if(t){const he=v.run();if(n||U||(te?he.some((be,Ae)=>Or(be,G[Ae])):Or(he,G))){D&&D();const be=en;en=v;try{const Ae=[he,G===gi?void 0:te&&G[0]===gi?[]:G,k];h?h(t,3,Ae):t(...Ae),G=he}finally{en=be}}}else v.run()};return c&&c(we),v=new Hc(w),v.scheduler=u?()=>u(we,!1):we,k=Q=>Mv(Q,!1,v),D=v.onStop=()=>{const Q=vi.get(v);if(Q){if(h)h(Q,4);else for(const he of Q)he();vi.delete(v)}},{}.NODE_ENV!=="production"&&(v.onTrack=s.onTrack,v.onTrigger=s.onTrigger),t?i?we(!0):G=v.run():u?u(we.bind(null,!0),!0):v.run(),se.pause=v.pause.bind(v),se.resume=v.resume.bind(v),se.stop=se,se}function or(e,t=1/0,s){if(t<=0||!Xe(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Dt(e))or(e.value,t,s);else if(ge(e))for(let i=0;i<e.length;i++)or(e[i],t,s);else if(Sn(e)||Yr(e))e.forEach(i=>{or(i,t,s)});else if(Fc(e)){for(const i in e)or(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&or(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const tn=[];function _i(e){tn.push(e)}function yi(){tn.pop()}let ml=!1;function Y(e,...t){if(ml)return;ml=!0,tr();const s=tn.length?tn[tn.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=kv();if(i)Nn(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${Li(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...Rv(n)),console.warn(...a)}sr(),ml=!1}function kv(){let e=tn[tn.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function Rv(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...Vv(s))}),t}function Vv({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Li(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...Fv(e.props),a]:[n+a]}function Fv(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...hd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function hd(e,t,s){return ft(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Dt(t)?(t=hd(e,Te(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):Se(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Te(t),s?t:[`${e}=`,t])}function Uv(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Y(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Y(`${t} is NaN - the duration expression might be incorrect.`))}const gl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Nn(e,t,s,i){try{return i?e(...i):e()}catch(n){ho(n,t,s)}}function Ps(e,t,s,i){if(Se(e)){const n=Nn(e,t,s,i);return n&&Za(n)&&n.catch(a=>{ho(a,t,s)}),n}if(ge(e)){const n=[];for(let a=0;a<e.length;a++)n.push(Ps(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&Y(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function ho(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||nt;if(t){let c=t.parent;const h=t.proxy,m={}.NODE_ENV!=="production"?gl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let v=0;v<p.length;v++)if(p[v](e,h,m)===!1)return}c=c.parent}if(a){tr(),Nn(a,null,10,[e,h,m]),sr();return}}Lv(e,s,n,i,u)}function Lv(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=gl[t];if(s&&_i(s),Y(`Unhandled error${a?` during execution of ${a}`:""}`),s&&yi(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const Qt=[];let Ws=-1;const Tn=[];let Nr=null,An=0;const pd=Promise.resolve();let bi=null;const Bv=100;function vl(e){const t=bi||pd;return e?t.then(this?e.bind(this):e):t}function $v(e){let t=Ws+1,s=Qt.length;for(;t<s;){const i=t+s>>>1,n=Qt[i],a=po(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function wi(e){if(!(e.flags&1)){const t=po(e),s=Qt[Qt.length-1];!s||!(e.flags&2)&&t>=po(s)?Qt.push(e):Qt.splice($v(t),0,e),e.flags|=1,md()}}function md(){bi||(bi=pd.then(yd))}function gd(e){ge(e)?Tn.push(...e):Nr&&e.id===-1?Nr.splice(An+1,0,e):e.flags&1||(Tn.push(e),e.flags|=1),md()}function vd(e,t,s=Ws+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Qt.length;s++){const i=Qt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&_l(t,i))continue;Qt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function _d(e){if(Tn.length){const t=[...new Set(Tn)].sort((s,i)=>po(s)-po(i));if(Tn.length=0,Nr){Nr.push(...t);return}for(Nr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),An=0;An<Nr.length;An++){const s=Nr[An];({}).NODE_ENV!=="production"&&_l(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Nr=null,An=0}}const po=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>_l(e,s):Ot;try{for(Ws=0;Ws<Qt.length;Ws++){const s=Qt[Ws];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Nn(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ws<Qt.length;Ws++){const s=Qt[Ws];s&&(s.flags&=-2)}Ws=-1,Qt.length=0,_d(e),bi=null,(Qt.length||Tn.length)&&yd(e)}}function _l(e,t){const s=e.get(t)||0;if(s>Bv){const i=t.i,n=i&&ql(i.type);return ho(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let ks=!1;const Ei=new Map;({}).NODE_ENV!=="production"&&(oo().__VUE_HMR_RUNTIME__={createRecord:yl(bd),rerender:yl(qv),reload:yl(zv)});const sn=new Map;function jv(e){const t=e.type.__hmrId;let s=sn.get(t);s||(bd(t,e.type),s=sn.get(t)),s.instances.add(e)}function Hv(e){sn.get(e.type.__hmrId).instances.delete(e)}function bd(e,t){return sn.has(e)?!1:(sn.set(e,{initialDef:Ci(t),instances:new Set}),!0)}function Ci(e){return Pf(e)?e.__vccOpts:e}function qv(e,t){const s=sn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ci(i.type).render=t),i.renderCache=[],ks=!0,i.update(),ks=!1}))}function zv(e,t){const s=sn.get(e);if(!s)return;t=Ci(t),wd(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=Ci(a.type);let c=Ei.get(u);c||(u!==s.initialDef&&wd(u,t),Ei.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?wi(()=>{ks=!0,a.parent.update(),ks=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}gd(()=>{Ei.clear()})}function wd(e,t){pt(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function yl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,mo=[],bl=!1;function go(e,...t){Gs?Gs.emit(e,...t):bl||mo.push({event:e,args:t})}function Ed(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,mo.forEach(({event:n,args:a})=>Gs.emit(n,...a)),mo=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{Ed(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,bl=!0,mo=[])},3e3)):(bl=!0,mo=[])}function Wv(e,t){go("app:init",e,t,{Fragment:Me,Text:Eo,Comment:wt,Static:Co})}function Gv(e){go("app:unmount",e)}const Kv=wl("component:added"),Cd=wl("component:updated"),Yv=wl("component:removed"),Qv=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&Yv(e)};/*! #__NO_SIDE_EFFECTS__ */function wl(e){return t=>{go(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const Zv=Dd("perf:start"),Jv=Dd("perf:end");function Dd(e){return(t,s,i)=>{go(e,t.appContext.app,t.uid,t,s,i)}}function Xv(e,t,s){go("component:emit",e.appContext.app,e,t,s)}let xt=null,xd=null;function Di(e){const t=xt;return xt=e,xd=e&&e.type.__scopeId||null,t}function ye(e,t=xt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&Cf(-1);const a=Di(t);let u;try{u=e(...n)}finally{Di(a),i._d&&Cf(1)}return{}.NODE_ENV!=="production"&&Cd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Sd(e){zg(e)&&Y("Do not use built-in directive ids as custom directive id: "+e)}function vt(e,t){if(xt===null)return{}.NODE_ENV!=="production"&&Y("withDirectives can only be used inside render functions."),e;const s=Ui(xt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,h=nt]=t[n];a&&(Se(a)&&(a={mounted:a,updated:a}),a.deep&&or(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function rn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(tr(),Ps(h,s,8,[e.el,c,e,t]),sr())}}const Od=Symbol("_vte"),Id=e=>e.__isTeleport,nn=e=>e&&(e.disabled||e.disabled===""),Nd=e=>e&&(e.defer||e.defer===""),Td=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ad=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,El=(e,t)=>{const s=e&&e.to;if(ft(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!nn(e)&&Y(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Y("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!nn(e)&&Y(`Invalid Teleport target: ${s}`),s},Md={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,h,m){const{mc:p,pc:v,pbc:w,o:{insert:D,querySelector:k,createText:U,createComment:te}}=m,T=nn(t.props);let{shapeFlag:se,children:G,dynamicChildren:we}=t;if({}.NODE_ENV!=="production"&&ks&&(h=!1,we=null),e==null){const Q=t.el={}.NODE_ENV!=="production"?te("teleport start"):U(""),he=t.anchor={}.NODE_ENV!=="production"?te("teleport end"):U("");D(Q,s,i),D(he,s,i);const be=(le,ie)=>{se&16&&(n&&n.isCE&&(n.ce._teleportTarget=le),p(G,le,ie,n,a,u,c,h))},Ae=()=>{const le=t.target=El(t.props,k),ie=Pd(le,t,U,D);le?(u!=="svg"&&Td(le)?u="svg":u!=="mathml"&&Ad(le)&&(u="mathml"),T||(be(le,ie),Si(t,!1))):{}.NODE_ENV!=="production"&&!T&&Y("Invalid Teleport target on mount:",le,`(${typeof le})`)};T&&(be(s,he),Si(t,!0)),Nd(t.props)?Jt(()=>{Ae(),t.el.__isMounted=!0},a):Ae()}else{if(Nd(t.props)&&!e.el.__isMounted){Jt(()=>{Md.process(e,t,s,i,n,a,u,c,h,m),delete e.el.__isMounted},a);return}t.el=e.el,t.targetStart=e.targetStart;const Q=t.anchor=e.anchor,he=t.target=e.target,be=t.targetAnchor=e.targetAnchor,Ae=nn(e.props),le=Ae?s:he,ie=Ae?Q:be;if(u==="svg"||Td(he)?u="svg":(u==="mathml"||Ad(he))&&(u="mathml"),we?(w(e.dynamicChildren,we,le,n,a,u,c),wo(e,t,!0)):h||v(e,t,le,ie,n,a,u,c,!1),T)Ae?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):xi(t,s,Q,m,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const ke=t.target=El(t.props,k);ke?xi(t,ke,null,m,0):{}.NODE_ENV!=="production"&&Y("Invalid Teleport target on update:",he,`(${typeof he})`)}else Ae&&xi(t,he,be,m,1);Si(t,T)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:m,targetAnchor:p,target:v,props:w}=e;if(v&&(n(m),n(p)),a&&n(h),u&16){const D=a||!nn(w);for(let k=0;k<c.length;k++){const U=c[k];i(U,t,s,D,!!U.dynamicChildren)}}},move:xi,hydrate:e_};function xi(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:m,props:p}=e,v=a===2;if(v&&i(u,t,s),(!v||nn(p))&&h&16)for(let w=0;w<m.length;w++)n(m[w],t,s,2);v&&i(c,t,s)}function e_(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:m,createText:p}},v){const w=t.target=El(t.props,h);if(w){const D=nn(t.props),k=w._lpa||w.firstChild;if(t.shapeFlag&16)if(D)t.anchor=v(u(e),t,c(e),s,i,n,a),t.targetStart=k,t.targetAnchor=k&&u(k);else{t.anchor=u(e);let U=k;for(;U;){if(U&&U.nodeType===8){if(U.data==="teleport start anchor")t.targetStart=U;else if(U.data==="teleport anchor"){t.targetAnchor=U,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}U=u(U)}t.targetAnchor||Pd(w,t,p,m),v(k&&u(k),t,w,s,i,n,a)}Si(t,D)}return t.anchor&&u(t.anchor)}const t_=Md;function Si(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Pd(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[Od]=a,e&&(i(n,e),i(a,e)),a}const Tr=Symbol("_leaveCb"),Oi=Symbol("_enterCb");function s_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Hd(()=>{e.isMounted=!0}),qd(()=>{e.isUnmounting=!0}),e}const bs=[Function,Array],kd={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:bs,onEnter:bs,onAfterEnter:bs,onEnterCancelled:bs,onBeforeLeave:bs,onLeave:bs,onAfterLeave:bs,onLeaveCancelled:bs,onBeforeAppear:bs,onAppear:bs,onAfterAppear:bs,onAppearCancelled:bs},Rd=e=>{const t=e.subTree;return t.component?Rd(t.component):t},r_={name:"BaseTransition",props:kd,setup(e,{slots:t}){const s=Vi(),i=s_();return()=>{const n=t.default&&Ld(t.default(),!0);if(!n||!n.length)return;const a=Vd(n),u=Te(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Y(`invalid <transition> mode: ${c}`),i.isLeaving)return Dl(a);const h=Ud(a);if(!h)return Dl(a);let m=Cl(h,u,i,s,v=>m=v);h.type!==wt&&vo(h,m);let p=s.subTree&&Ud(s.subTree);if(p&&p.type!==wt&&!un(h,p)&&Rd(s).type!==wt){let v=Cl(p,u,i,s);if(vo(p,v),c==="out-in"&&h.type!==wt)return i.isLeaving=!0,v.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete v.afterLeave,p=void 0},Dl(a);c==="in-out"&&h.type!==wt?v.delayLeave=(w,D,k)=>{const U=Fd(i,p);U[String(p.key)]=p,w[Tr]=()=>{D(),w[Tr]=void 0,delete m.delayedLeave,p=void 0},m.delayedLeave=()=>{k(),delete m.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Vd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==wt){if({}.NODE_ENV!=="production"&&s){Y("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const n_=r_;function Fd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function Cl(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:m,onAfterEnter:p,onEnterCancelled:v,onBeforeLeave:w,onLeave:D,onAfterLeave:k,onLeaveCancelled:U,onBeforeAppear:te,onAppear:T,onAfterAppear:se,onAppearCancelled:G}=t,we=String(e.key),Q=Fd(s,e),he=(le,ie)=>{le&&Ps(le,i,9,ie)},be=(le,ie)=>{const ke=ie[1];he(le,ie),ge(le)?le.every(ae=>ae.length<=1)&&ke():le.length<=1&&ke()},Ae={mode:u,persisted:c,beforeEnter(le){let ie=h;if(!s.isMounted)if(a)ie=te||h;else return;le[Tr]&&le[Tr](!0);const ke=Q[we];ke&&un(e,ke)&&ke.el[Tr]&&ke.el[Tr](),he(ie,[le])},enter(le){let ie=m,ke=p,ae=v;if(!s.isMounted)if(a)ie=T||m,ke=se||p,ae=G||v;else return;let We=!1;const J=le[Oi]=$e=>{We||(We=!0,$e?he(ae,[le]):he(ke,[le]),Ae.delayedLeave&&Ae.delayedLeave(),le[Oi]=void 0)};ie?be(ie,[le,J]):J()},leave(le,ie){const ke=String(e.key);if(le[Oi]&&le[Oi](!0),s.isUnmounting)return ie();he(w,[le]);let ae=!1;const We=le[Tr]=J=>{ae||(ae=!0,ie(),J?he(U,[le]):he(k,[le]),le[Tr]=void 0,Q[ke]===e&&delete Q[ke])};Q[ke]=e,D?be(D,[le,We]):We()},clone(le){const ie=Cl(le,t,s,i,n);return n&&n(ie),ie}};return Ae}function Dl(e){if(_o(e))return e=Ks(e),e.children=null,e}function Ud(e){if(!_o(e))return Id(e.type)&&e.children?Vd(e.children):e;if({}.NODE_ENV!=="production"&&e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&Se(s.default))return s.default()}}function vo(e,t){e.shapeFlag&6&&e.component?(e.transition=t,vo(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ld(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Me?(u.patchFlag&128&&n++,i=i.concat(Ld(u.children,t,c))):(t||u.type!==wt)&&i.push(c!=null?Ks(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Bd(e,t){return Se(e)?(()=>pt({name:e.name},t,{setup:e}))():e}function $d(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const o_=new WeakSet;function Ii(e,t,s,i,n=!1){if(ge(e)){e.forEach((k,U)=>Ii(k,t&&(ge(t)?t[U]:t),s,i,n));return}if(Mn(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Ii(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Ui(i.component):i.el,u=n?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){Y("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const m=t&&t.r,p=c.refs===nt?c.refs={}:c.refs,v=c.setupState,w=Te(v),D=v===nt?()=>!1:k=>({}).NODE_ENV!=="production"&&(Ze(w,k)&&!Dt(w[k])&&Y(`Template ref "${k}" used on a non-ref value. It will not work in the production build.`),o_.has(w[k]))?!1:Ze(w,k);if(m!=null&&m!==h&&(ft(m)?(p[m]=null,D(m)&&(v[m]=null)):Dt(m)&&(m.value=null)),Se(h))Nn(h,c,12,[u,p]);else{const k=ft(h),U=Dt(h);if(k||U){const te=()=>{if(e.f){const T=k?D(h)?v[h]:p[h]:h.value;n?ge(T)&&Qa(T,a):ge(T)?T.includes(a)||T.push(a):k?(p[h]=[a],D(h)&&(v[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else k?(p[h]=u,D(h)&&(v[h]=u)):U?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Y("Invalid template ref type:",h,`(${typeof h})`)};u?(te.id=-1,Jt(te,s)):te()}else({}).NODE_ENV!=="production"&&Y("Invalid template ref type:",h,`(${typeof h})`)}}oo().requestIdleCallback,oo().cancelIdleCallback;const Mn=e=>!!e.type.__asyncLoader,_o=e=>e.type.__isKeepAlive;function i_(e,t){jd(e,"a",t)}function a_(e,t){jd(e,"da",t)}function jd(e,t,s=Nt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Ni(t,i,s),s){let n=s.parent;for(;n&&n.parent;)_o(n.parent.vnode)&&l_(i,t,s,n),n=n.parent}}function l_(e,t,s,i){const n=Ni(t,e,i,!0);zd(()=>{Qa(i[t],n)},s)}function Ni(e,t,s=Nt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{tr();const c=So(s),h=Ps(t,s,e,u);return c(),sr(),h});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=Zr(gl[e].replace(/ hook$/,""));Y(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=Nt)=>{(!Oo||e==="sp")&&Ni(e,(...i)=>t(...i),s)},u_=ir("bm"),Hd=ir("m"),c_=ir("bu"),d_=ir("u"),qd=ir("bum"),zd=ir("um"),f_=ir("sp"),h_=ir("rtg"),p_=ir("rtc");function m_(e,t=Nt){Ni("ec",e,t)}const xl="components",g_="directives";function X(e,t){return Wd(xl,e,!0,t)||e}const v_=Symbol.for("v-ndc");function __(e){return Wd(g_,e)}function Wd(e,t,s=!0,i=!1){const n=xt||Nt;if(n){const a=n.type;if(e===xl){const c=ql(a,!1);if(c&&(c===t||c===Kt(t)||c===Qr(Kt(t))))return a}const u=Gd(n[e]||a[e],t)||Gd(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===xl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Y(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Y(`resolve${Qr(e.slice(0,-1))} can only be used in render() or setup().`)}function Gd(e,t){return e&&(e[t]||e[Kt(t)]||e[Qr(Kt(t))])}function mt(e,t,s,i){let n;const a=s&&s[i],u=ge(e);if(u||ft(e)){const c=u&&Xr(e);let h=!1;c&&(h=!Yt(e),e=ui(e)),n=new Array(e.length);for(let m=0,p=e.length;m<p;m++)n[m]=t(h?Ht(e[m]):e[m],m,void 0,a&&a[m])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Y(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(Xe(e))if(e[Symbol.iterator])n=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);n=new Array(c.length);for(let h=0,m=c.length;h<m;h++){const p=c[h];n[h]=t(e[p],p,h,a&&a[h])}}else n=[];return s&&(s[i]=n),n}function Vt(e,t,s={},i,n){if(xt.ce||xt.parent&&Mn(xt.parent)&&xt.parent.ce)return t!=="default"&&(s.name=t),O(),Pt(Me,null,[A("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Y("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),O();const u=a&&Kd(a(s)),c=s.key||u&&u.key,h=Pt(Me,{key:(c&&!As(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function Kd(e){return e.some(t=>ln(t)?!(t.type===wt||t.type===Me&&!Kd(t.children)):!0)?e:null}const Sl=e=>e?Nf(e)?Ui(e):Sl(e.parent):null,on=pt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?zs(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?zs(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?zs(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?zs(e.refs):e.refs,$parent:e=>Sl(e.parent),$root:e=>Sl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Tl(e),$forceUpdate:e=>e.f||(e.f=()=>{wi(e.update)}),$nextTick:e=>e.n||(e.n=vl.bind(e.proxy)),$watch:e=>Z_.bind(e)}),Ol=e=>e==="_"||e==="$",Il=(e,t)=>e!==nt&&!e.__isScriptSetup&&Ze(e,t),Yd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let m;if(t[0]!=="$"){const D=u[t];if(D!==void 0)switch(D){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(Il(i,t))return u[t]=1,i[t];if(n!==nt&&Ze(n,t))return u[t]=2,n[t];if((m=e.propsOptions[0])&&Ze(m,t))return u[t]=3,a[t];if(s!==nt&&Ze(s,t))return u[t]=4,s[t];Nl&&(u[t]=0)}}const p=on[t];let v,w;if(p)return t==="$attrs"?(It(e.attrs,"get",""),{}.NODE_ENV!=="production"&&ki()):{}.NODE_ENV!=="production"&&t==="$slots"&&It(e,"get",t),p(e);if((v=c.__cssModules)&&(v=v[t]))return v;if(s!==nt&&Ze(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ze(w,t))return w[t];({}).NODE_ENV!=="production"&&xt&&(!ft(t)||t.indexOf("__v")!==0)&&(n!==nt&&Ol(t[0])&&Ze(n,t)?Y(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===xt&&Y(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return Il(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&Ze(n,t)?(Y(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==nt&&Ze(i,t)?(i[t]=s,!0):Ze(e.props,t)?({}.NODE_ENV!=="production"&&Y(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Y(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==nt&&Ze(e,u)||Il(t,u)||(c=a[0])&&Ze(c,u)||Ze(i,u)||Ze(on,u)||Ze(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ze(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Yd.ownKeys=e=>(Y("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function y_(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(on).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>on[s](e),set:Ot})}),t}function b_(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Ot})})}function w_(e){const{ctx:t,setupState:s}=e;Object.keys(Te(s)).forEach(i=>{if(!s.__isScriptSetup){if(Ol(i[0])){Y(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Ot})}})}function Qd(e){return ge(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function E_(){const e=Object.create(null);return(t,s)=>{e[s]?Y(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Nl=!0;function C_(e){const t=Tl(e),s=e.proxy,i=e.ctx;Nl=!1,t.beforeCreate&&Zd(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:h,inject:m,created:p,beforeMount:v,mounted:w,beforeUpdate:D,updated:k,activated:U,deactivated:te,beforeDestroy:T,beforeUnmount:se,destroyed:G,unmounted:we,render:Q,renderTracked:he,renderTriggered:be,errorCaptured:Ae,serverPrefetch:le,expose:ie,inheritAttrs:ke,components:ae,directives:We,filters:J}=t,$e={}.NODE_ENV!=="production"?E_():null;if({}.NODE_ENV!=="production"){const[Oe]=e.propsOptions;if(Oe)for(const Ee in Oe)$e("Props",Ee)}if(m&&D_(m,i,$e),u)for(const Oe in u){const Ee=u[Oe];Se(Ee)?({}.NODE_ENV!=="production"?Object.defineProperty(i,Oe,{value:Ee.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[Oe]=Ee.bind(s),{}.NODE_ENV!=="production"&&$e("Methods",Oe)):{}.NODE_ENV!=="production"&&Y(`Method "${Oe}" has type "${typeof Ee}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!Se(n)&&Y("The data option must be a function. Plain object usage is no longer supported.");const Oe=n.call(s,s);if({}.NODE_ENV!=="production"&&Za(Oe)&&Y("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Xe(Oe))({}).NODE_ENV!=="production"&&Y("data() should return an object.");else if(e.data=hi(Oe),{}.NODE_ENV!=="production")for(const Ee in Oe)$e("Data",Ee),Ol(Ee[0])||Object.defineProperty(i,Ee,{configurable:!0,enumerable:!0,get:()=>Oe[Ee],set:Ot})}if(Nl=!0,a)for(const Oe in a){const Ee=a[Oe],Ut=Se(Ee)?Ee.bind(s,s):Se(Ee.get)?Ee.get.bind(s,s):Ot;({}).NODE_ENV!=="production"&&Ut===Ot&&Y(`Computed property "${Oe}" has no getter.`);const Xt=!Se(Ee)&&Se(Ee.set)?Ee.set.bind(s):{}.NODE_ENV!=="production"?()=>{Y(`Write operation failed: computed property "${Oe}" is readonly.`)}:Ot,yt=Fs({get:Ut,set:Xt});Object.defineProperty(i,Oe,{enumerable:!0,configurable:!0,get:()=>yt.value,set:de=>yt.value=de}),{}.NODE_ENV!=="production"&&$e("Computed",Oe)}if(c)for(const Oe in c)Jd(c[Oe],i,s,Oe);if(h){const Oe=Se(h)?h.call(s):h;Reflect.ownKeys(Oe).forEach(Ee=>{Ai(Ee,Oe[Ee])})}p&&Zd(p,e,"c");function ut(Oe,Ee){ge(Ee)?Ee.forEach(Ut=>Oe(Ut.bind(s))):Ee&&Oe(Ee.bind(s))}if(ut(u_,v),ut(Hd,w),ut(c_,D),ut(d_,k),ut(i_,U),ut(a_,te),ut(m_,Ae),ut(p_,he),ut(h_,be),ut(qd,se),ut(zd,we),ut(f_,le),ge(ie))if(ie.length){const Oe=e.exposed||(e.exposed={});ie.forEach(Ee=>{Object.defineProperty(Oe,Ee,{get:()=>s[Ee],set:Ut=>s[Ee]=Ut})})}else e.exposed||(e.exposed={});Q&&e.render===Ot&&(e.render=Q),ke!=null&&(e.inheritAttrs=ke),ae&&(e.components=ae),We&&(e.directives=We),le&&$d(e)}function D_(e,t,s=Ot){ge(e)&&(e=Al(e));for(const i in e){const n=e[i];let a;Xe(n)?"default"in n?a=Rs(n.from||i,n.default,!0):a=Rs(n.from||i):a=Rs(n),Dt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Zd(e,t,s){Ps(ge(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Jd(e,t,s,i){let n=i.includes(".")?vf(s,i):()=>s[i];if(ft(e)){const a=t[e];Se(a)?kn(n,a):{}.NODE_ENV!=="production"&&Y(`Invalid watch handler specified by key "${e}"`,a)}else if(Se(e))kn(n,e.bind(s));else if(Xe(e))if(ge(e))e.forEach(a=>Jd(a,t,s,i));else{const a=Se(e.handler)?e.handler.bind(s):t[e.handler];Se(a)?kn(n,a,e):{}.NODE_ENV!=="production"&&Y(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Y(`Invalid watch option: "${i}"`,e)}function Tl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!n.length&&!s&&!i?h=t:(h={},n.length&&n.forEach(m=>Ti(h,m,u,!0)),Ti(h,t,u)),Xe(t)&&a.set(t,h),h}function Ti(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&Ti(e,a,s,!0),n&&n.forEach(u=>Ti(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Y('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=x_[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const x_={data:Xd,props:ef,emits:ef,methods:yo,computed:yo,beforeCreate:Zt,created:Zt,beforeMount:Zt,mounted:Zt,beforeUpdate:Zt,updated:Zt,beforeDestroy:Zt,beforeUnmount:Zt,destroyed:Zt,unmounted:Zt,activated:Zt,deactivated:Zt,errorCaptured:Zt,serverPrefetch:Zt,components:yo,directives:yo,watch:O_,provide:Xd,inject:S_};function Xd(e,t){return t?e?function(){return pt(Se(e)?e.call(this,this):e,Se(t)?t.call(this,this):t)}:t:e}function S_(e,t){return yo(Al(e),Al(t))}function Al(e){if(ge(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Zt(e,t){return e?[...new Set([].concat(e,t))]:t}function yo(e,t){return e?pt(Object.create(null),e,t):t}function ef(e,t){return e?ge(e)&&ge(t)?[...new Set([...e,...t])]:pt(Object.create(null),Qd(e),Qd(t??{})):t}function O_(e,t){if(!e)return t;if(!t)return e;const s=pt(Object.create(null),e);for(const i in t)s[i]=Zt(e[i],t[i]);return s}function tf(){return{app:null,config:{isNativeTag:Hg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let I_=0;function N_(e,t){return function(i,n=null){Se(i)||(i=pt({},i)),n!=null&&!Xe(n)&&({}.NODE_ENV!=="production"&&Y("root props passed to app.mount() must be an object."),n=null);const a=tf(),u=new WeakSet,c=[];let h=!1;const m=a.app={_uid:I_++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:kf,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Y("app.config cannot be replaced. Modify individual options instead.")},use(p,...v){return u.has(p)?{}.NODE_ENV!=="production"&&Y("Plugin has already been applied to target app."):p&&Se(p.install)?(u.add(p),p.install(m,...v)):Se(p)?(u.add(p),p(m,...v)):{}.NODE_ENV!=="production"&&Y('A plugin must either be a function or an object with an "install" function.'),m},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Y("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),m},component(p,v){return{}.NODE_ENV!=="production"&&jl(p,a.config),v?({}.NODE_ENV!=="production"&&a.components[p]&&Y(`Component "${p}" has already been registered in target app.`),a.components[p]=v,m):a.components[p]},directive(p,v){return{}.NODE_ENV!=="production"&&Sd(p),v?({}.NODE_ENV!=="production"&&a.directives[p]&&Y(`Directive "${p}" has already been registered in target app.`),a.directives[p]=v,m):a.directives[p]},mount(p,v,w){if(h)({}).NODE_ENV!=="production"&&Y("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Y("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const D=m._ceVNode||A(i,n);return D.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{e(Ks(D),p,w)}),v&&t?t(D,p):e(D,p,w),h=!0,m._container=p,p.__vue_app__=m,{}.NODE_ENV!=="production"&&(m._instance=D.component,Wv(m,kf)),Ui(D.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Y(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ps(c,m._instance,16),e(null,m._container),{}.NODE_ENV!=="production"&&(m._instance=null,Gv(m)),delete m._container.__vue_app__):{}.NODE_ENV!=="production"&&Y("Cannot unmount an app that is not mounted.")},provide(p,v){return{}.NODE_ENV!=="production"&&p in a.provides&&Y(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`),a.provides[p]=v,m},runWithContext(p){const v=Pn;Pn=m;try{return p()}finally{Pn=v}}};return m}}let Pn=null;function Ai(e,t){if(!Nt)({}).NODE_ENV!=="production"&&Y("provide() can only be used inside setup().");else{let s=Nt.provides;const i=Nt.parent&&Nt.parent.provides;i===s&&(s=Nt.provides=Object.create(i)),s[e]=t}}function Rs(e,t,s=!1){const i=Nt||xt;if(i||Pn){const n=Pn?Pn._context.provides:i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&Se(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Y(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Y("inject() can only be used inside setup() or functional components.")}const sf={},rf=()=>Object.create(sf),nf=e=>Object.getPrototypeOf(e)===sf;function T_(e,t,s,i=!1){const n={},a=rf();e.propsDefaults=Object.create(null),of(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&uf(t||{},n,e),s?e.props=i?n:ld(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function A_(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function M_(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Te(n),[h]=e.propsOptions;let m=!1;if(!({}.NODE_ENV!=="production"&&A_(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let v=0;v<p.length;v++){let w=p[v];if(Pi(e.emitsOptions,w))continue;const D=t[w];if(h)if(Ze(a,w))D!==a[w]&&(a[w]=D,m=!0);else{const k=Kt(w);n[k]=Ml(h,c,k,D,e,!1)}else D!==a[w]&&(a[w]=D,m=!0)}}}else{of(e,t,n,a)&&(m=!0);let p;for(const v in c)(!t||!Ze(t,v)&&((p=Sr(v))===v||!Ze(t,p)))&&(h?s&&(s[v]!==void 0||s[p]!==void 0)&&(n[v]=Ml(h,c,v,void 0,e,!0)):delete n[v]);if(a!==c)for(const v in a)(!t||!Ze(t,v))&&(delete a[v],m=!0)}m&&qs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&uf(t||{},n,e)}function of(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(no(h))continue;const m=t[h];let p;n&&Ze(n,p=Kt(h))?!a||!a.includes(p)?s[p]=m:(c||(c={}))[p]=m:Pi(e.emitsOptions,h)||(!(h in i)||m!==i[h])&&(i[h]=m,u=!0)}if(a){const h=Te(s),m=c||nt;for(let p=0;p<a.length;p++){const v=a[p];s[v]=Ml(n,h,v,m[v],e,!Ze(m,v))}}return u}function Ml(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=Ze(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&Se(h)){const{propsDefaults:m}=n;if(s in m)i=m[s];else{const p=So(n);i=m[s]=h.call(null,t),p()}}else i=h;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Sr(s))&&(i=!0))}return i}const P_=new WeakMap;function af(e,t,s=!1){const i=s?P_:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let h=!1;if(!Se(e)){const p=v=>{h=!0;const[w,D]=af(v,t,!0);pt(u,w),D&&c.push(...D)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Xe(e)&&i.set(e,xn),xn;if(ge(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!ft(a[p])&&Y("props must be strings when using array syntax.",a[p]);const v=Kt(a[p]);lf(v)&&(u[v]=nt)}else if(a){({}).NODE_ENV!=="production"&&!Xe(a)&&Y("invalid props options",a);for(const p in a){const v=Kt(p);if(lf(v)){const w=a[p],D=u[v]=ge(w)||Se(w)?{type:w}:pt({},w),k=D.type;let U=!1,te=!0;if(ge(k))for(let T=0;T<k.length;++T){const se=k[T],G=Se(se)&&se.name;if(G==="Boolean"){U=!0;break}else G==="String"&&(te=!1)}else U=Se(k)&&k.name==="Boolean";D[0]=U,D[1]=te,(U||Ze(D,"default"))&&c.push(v)}}}const m=[u,c];return Xe(e)&&i.set(e,m),m}function lf(e){return e[0]!=="$"&&!no(e)?!0:({}.NODE_ENV!=="production"&&Y(`Invalid prop name: "${e}" is a reserved property.`),!1)}function k_(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function uf(e,t,s){const i=Te(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in n){let c=n[u];c!=null&&R_(u,i[u],c,{}.NODE_ENV!=="production"?zs(i):i,!a.includes(u))}}function R_(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&n){Y('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let m=!1;const p=ge(a)?a:[a],v=[];for(let w=0;w<p.length&&!m;w++){const{valid:D,expectedType:k}=F_(t,p[w]);v.push(k||""),m=D}if(!m){Y(U_(e,t,v));return}}c&&!c(t,i)&&Y('Invalid prop: custom validator check failed for prop "'+e+'".')}}const V_=er("String,Number,Boolean,Function,Symbol,BigInt");function F_(e,t){let s;const i=k_(t);if(i==="null")s=e===null;else if(V_(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=Xe(e):i==="Array"?s=ge(e):s=e instanceof t;return{valid:s,expectedType:i}}function U_(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Qr).join(" | ")}`;const n=s[0],a=Ja(t),u=cf(t,n),c=cf(t,a);return s.length===1&&df(n)&&!L_(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,df(a)&&(i+=`with value ${c}.`),i}function cf(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function df(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function L_(...e){return e.some(t=>t.toLowerCase()==="boolean")}const ff=e=>e[0]==="_"||e==="$stable",Pl=e=>ge(e)?e.map(Vs):[Vs(e)],B_=(e,t,s)=>{if(t._n)return t;const i=ye((...n)=>({}.NODE_ENV!=="production"&&Nt&&(!s||s.root===Nt.root)&&Y(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Pl(t(...n))),s);return i._c=!1,i},hf=(e,t,s)=>{const i=e._ctx;for(const n in e){if(ff(n))continue;const a=e[n];if(Se(a))t[n]=B_(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Y(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=Pl(a);t[n]=()=>u}}},pf=(e,t)=>{({}).NODE_ENV!=="production"&&!_o(e.vnode)&&Y("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Pl(t);e.slots.default=()=>s},kl=(e,t,s)=>{for(const i in t)(s||i!=="_")&&(e[i]=t[i])},$_=(e,t,s)=>{const i=e.slots=rf();if(e.vnode.shapeFlag&32){const n=t._;n?(kl(i,t,s),s&&ai(i,"_",n,!0)):hf(t,i)}else t&&pf(e,t)},j_=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=nt;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&ks?(kl(n,t,s),qs(e,"set","$slots")):s&&c===1?a=!1:kl(n,t,s):(a=!t.$stable,hf(t,n)),u=t}else t&&(pf(e,t),u={default:1});if(a)for(const c in n)!ff(c)&&u[c]==null&&delete n[c]};let bo,Ar;function ar(e,t){e.appContext.config.performance&&Mi()&&Ar.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&Zv(e,t,Mi()?Ar.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Mi()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ar.mark(i),Ar.measure(`<${Li(e,e.type)}> ${t}`,s,i),Ar.clearMarks(s),Ar.clearMarks(i)}({}).NODE_ENV!=="production"&&Jv(e,t,Mi()?Ar.now():Date.now())}function Mi(){return bo!==void 0||(typeof window<"u"&&window.performance?(bo=!0,Ar=window.performance):bo=!1),bo}function H_(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Jt=ny;function q_(e){return z_(e)}function z_(e,t){H_();const s=oo();s.__VUE__=!0,{}.NODE_ENV!=="production"&&Ed(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:h,setText:m,setElementText:p,parentNode:v,nextSibling:w,setScopeId:D=Ot,insertStaticContent:k}=e,U=(b,C,P,F=null,j=null,q=null,ee=void 0,K=null,Z={}.NODE_ENV!=="production"&&ks?!1:!!C.dynamicChildren)=>{if(b===C)return;b&&!un(b,C)&&(F=oe(b),Ke(b,j,q,!0),b=null),C.patchFlag===-2&&(Z=!1,C.dynamicChildren=null);const{type:z,ref:Ce,shapeFlag:re}=C;switch(z){case Eo:te(b,C,P,F);break;case wt:T(b,C,P,F);break;case Co:b==null?se(C,P,F,ee):{}.NODE_ENV!=="production"&&G(b,C,P,ee);break;case Me:We(b,C,P,F,j,q,ee,K,Z);break;default:re&1?he(b,C,P,F,j,q,ee,K,Z):re&6?J(b,C,P,F,j,q,ee,K,Z):re&64||re&128?z.process(b,C,P,F,j,q,ee,K,Z,Re):{}.NODE_ENV!=="production"&&Y("Invalid VNode type:",z,`(${typeof z})`)}Ce!=null&&j&&Ii(Ce,b&&b.ref,q,C||b,!C)},te=(b,C,P,F)=>{if(b==null)i(C.el=c(C.children),P,F);else{const j=C.el=b.el;C.children!==b.children&&m(j,C.children)}},T=(b,C,P,F)=>{b==null?i(C.el=h(C.children||""),P,F):C.el=b.el},se=(b,C,P,F)=>{[b.el,b.anchor]=k(b.children,C,P,F,b.el,b.anchor)},G=(b,C,P,F)=>{if(C.children!==b.children){const j=w(b.anchor);Q(b),[C.el,C.anchor]=k(C.children,P,j,F)}else C.el=b.el,C.anchor=b.anchor},we=({el:b,anchor:C},P,F)=>{let j;for(;b&&b!==C;)j=w(b),i(b,P,F),b=j;i(C,P,F)},Q=({el:b,anchor:C})=>{let P;for(;b&&b!==C;)P=w(b),n(b),b=P;n(C)},he=(b,C,P,F,j,q,ee,K,Z)=>{C.type==="svg"?ee="svg":C.type==="math"&&(ee="mathml"),b==null?be(C,P,F,j,q,ee,K,Z):ie(b,C,j,q,ee,K,Z)},be=(b,C,P,F,j,q,ee,K)=>{let Z,z;const{props:Ce,shapeFlag:re,transition:_e,dirs:De}=b;if(Z=b.el=u(b.type,q,Ce&&Ce.is,Ce),re&8?p(Z,b.children):re&16&&le(b.children,Z,null,F,j,Rl(b,q),ee,K),De&&rn(b,null,F,"created"),Ae(Z,b,b.scopeId,ee,F),Ce){for(const st in Ce)st!=="value"&&!no(st)&&a(Z,st,null,Ce[st],q,F);"value"in Ce&&a(Z,"value",null,Ce.value,q),(z=Ce.onVnodeBeforeMount)&&Ys(z,F,b)}({}).NODE_ENV!=="production"&&(ai(Z,"__vnode",b,!0),ai(Z,"__vueParentComponent",F,!0)),De&&rn(b,null,F,"beforeMount");const Ue=W_(j,_e);Ue&&_e.beforeEnter(Z),i(Z,C,P),((z=Ce&&Ce.onVnodeMounted)||Ue||De)&&Jt(()=>{z&&Ys(z,F,b),Ue&&_e.enter(Z),De&&rn(b,null,F,"mounted")},j)},Ae=(b,C,P,F,j)=>{if(P&&D(b,P),F)for(let q=0;q<F.length;q++)D(b,F[q]);if(j){let q=j.subTree;if({}.NODE_ENV!=="production"&&q.patchFlag>0&&q.patchFlag&2048&&(q=Ll(q.children)||q),C===q||Ef(q.type)&&(q.ssContent===C||q.ssFallback===C)){const ee=j.vnode;Ae(b,ee,ee.scopeId,ee.slotScopeIds,j.parent)}}},le=(b,C,P,F,j,q,ee,K,Z=0)=>{for(let z=Z;z<b.length;z++){const Ce=b[z]=K?Mr(b[z]):Vs(b[z]);U(null,Ce,C,P,F,j,q,ee,K)}},ie=(b,C,P,F,j,q,ee)=>{const K=C.el=b.el;({}).NODE_ENV!=="production"&&(K.__vnode=C);let{patchFlag:Z,dynamicChildren:z,dirs:Ce}=C;Z|=b.patchFlag&16;const re=b.props||nt,_e=C.props||nt;let De;if(P&&an(P,!1),(De=_e.onVnodeBeforeUpdate)&&Ys(De,P,C,b),Ce&&rn(C,b,P,"beforeUpdate"),P&&an(P,!0),{}.NODE_ENV!=="production"&&ks&&(Z=0,ee=!1,z=null),(re.innerHTML&&_e.innerHTML==null||re.textContent&&_e.textContent==null)&&p(K,""),z?(ke(b.dynamicChildren,z,K,P,F,Rl(C,j),q),{}.NODE_ENV!=="production"&&wo(b,C)):ee||Ut(b,C,K,null,P,F,Rl(C,j),q,!1),Z>0){if(Z&16)ae(K,re,_e,P,j);else if(Z&2&&re.class!==_e.class&&a(K,"class",null,_e.class,j),Z&4&&a(K,"style",re.style,_e.style,j),Z&8){const Ue=C.dynamicProps;for(let st=0;st<Ue.length;st++){const Je=Ue[st],Lt=re[Je],St=_e[Je];(St!==Lt||Je==="value")&&a(K,Je,Lt,St,j,P)}}Z&1&&b.children!==C.children&&p(K,C.children)}else!ee&&z==null&&ae(K,re,_e,P,j);((De=_e.onVnodeUpdated)||Ce)&&Jt(()=>{De&&Ys(De,P,C,b),Ce&&rn(C,b,P,"updated")},F)},ke=(b,C,P,F,j,q,ee)=>{for(let K=0;K<C.length;K++){const Z=b[K],z=C[K],Ce=Z.el&&(Z.type===Me||!un(Z,z)||Z.shapeFlag&70)?v(Z.el):P;U(Z,z,Ce,null,F,j,q,ee,!0)}},ae=(b,C,P,F,j)=>{if(C!==P){if(C!==nt)for(const q in C)!no(q)&&!(q in P)&&a(b,q,C[q],null,j,F);for(const q in P){if(no(q))continue;const ee=P[q],K=C[q];ee!==K&&q!=="value"&&a(b,q,K,ee,j,F)}"value"in P&&a(b,"value",C.value,P.value,j)}},We=(b,C,P,F,j,q,ee,K,Z)=>{const z=C.el=b?b.el:c(""),Ce=C.anchor=b?b.anchor:c("");let{patchFlag:re,dynamicChildren:_e,slotScopeIds:De}=C;({}).NODE_ENV!=="production"&&(ks||re&2048)&&(re=0,Z=!1,_e=null),De&&(K=K?K.concat(De):De),b==null?(i(z,P,F),i(Ce,P,F),le(C.children||[],P,Ce,j,q,ee,K,Z)):re>0&&re&64&&_e&&b.dynamicChildren?(ke(b.dynamicChildren,_e,P,j,q,ee,K),{}.NODE_ENV!=="production"?wo(b,C):(C.key!=null||j&&C===j.subTree)&&wo(b,C,!0)):Ut(b,C,P,Ce,j,q,ee,K,Z)},J=(b,C,P,F,j,q,ee,K,Z)=>{C.slotScopeIds=K,b==null?C.shapeFlag&512?j.ctx.activate(C,P,F,ee,Z):$e(C,P,F,j,q,ee,Z):ut(b,C,Z)},$e=(b,C,P,F,j,q,ee)=>{const K=b.component=fy(b,F,j);if({}.NODE_ENV!=="production"&&K.type.__hmrId&&jv(K),{}.NODE_ENV!=="production"&&(_i(b),ar(K,"mount")),_o(b)&&(K.ctx.renderer=Re),{}.NODE_ENV!=="production"&&ar(K,"init"),py(K,!1,ee),{}.NODE_ENV!=="production"&&lr(K,"init"),K.asyncDep){if({}.NODE_ENV!=="production"&&ks&&(b.el=null),j&&j.registerDep(K,Oe,ee),!b.el){const Z=K.subTree=A(wt);T(null,Z,C,P)}}else Oe(K,b,C,P,j,q,ee);({}).NODE_ENV!=="production"&&(yi(),lr(K,"mount"))},ut=(b,C,P)=>{const F=C.component=b.component;if(sy(b,C,P))if(F.asyncDep&&!F.asyncResolved){({}).NODE_ENV!=="production"&&_i(C),Ee(F,C,P),{}.NODE_ENV!=="production"&&yi();return}else F.next=C,F.update();else C.el=b.el,F.vnode=C},Oe=(b,C,P,F,j,q,ee)=>{const K=()=>{if(b.isMounted){let{next:re,bu:_e,u:De,parent:Ue,vnode:st}=b;{const Bt=mf(b);if(Bt){re&&(re.el=st.el,Ee(b,re,ee)),Bt.asyncDep.then(()=>{b.isUnmounted||K()});return}}let Je=re,Lt;({}).NODE_ENV!=="production"&&_i(re||b.vnode),an(b,!1),re?(re.el=st.el,Ee(b,re,ee)):re=st,_e&&On(_e),(Lt=re.props&&re.props.onVnodeBeforeUpdate)&&Ys(Lt,Ue,re,st),an(b,!0),{}.NODE_ENV!=="production"&&ar(b,"render");const St=Ul(b);({}).NODE_ENV!=="production"&&lr(b,"render");const es=b.subTree;b.subTree=St,{}.NODE_ENV!=="production"&&ar(b,"patch"),U(es,St,v(es.el),oe(es),b,j,q),{}.NODE_ENV!=="production"&&lr(b,"patch"),re.el=St.el,Je===null&&ry(b,St.el),De&&Jt(De,j),(Lt=re.props&&re.props.onVnodeUpdated)&&Jt(()=>Ys(Lt,Ue,re,st),j),{}.NODE_ENV!=="production"&&Cd(b),{}.NODE_ENV!=="production"&&yi()}else{let re;const{el:_e,props:De}=C,{bm:Ue,m:st,parent:Je,root:Lt,type:St}=b,es=Mn(C);if(an(b,!1),Ue&&On(Ue),!es&&(re=De&&De.onVnodeBeforeMount)&&Ys(re,Je,C),an(b,!0),_e&&Ve){const Bt=()=>{({}).NODE_ENV!=="production"&&ar(b,"render"),b.subTree=Ul(b),{}.NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"hydrate"),Ve(_e,b.subTree,b,j,null),{}.NODE_ENV!=="production"&&lr(b,"hydrate")};es&&St.__asyncHydrate?St.__asyncHydrate(_e,b,Bt):Bt()}else{Lt.ce&&Lt.ce._injectChildStyle(St),{}.NODE_ENV!=="production"&&ar(b,"render");const Bt=b.subTree=Ul(b);({}).NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"patch"),U(null,Bt,P,F,b,j,q),{}.NODE_ENV!=="production"&&lr(b,"patch"),C.el=Bt.el}if(st&&Jt(st,j),!es&&(re=De&&De.onVnodeMounted)){const Bt=C;Jt(()=>Ys(re,Je,Bt),j)}(C.shapeFlag&256||Je&&Mn(Je.vnode)&&Je.vnode.shapeFlag&256)&&b.a&&Jt(b.a,j),b.isMounted=!0,{}.NODE_ENV!=="production"&&Kv(b),C=P=F=null}};b.scope.on();const Z=b.effect=new Hc(K);b.scope.off();const z=b.update=Z.run.bind(Z),Ce=b.job=Z.runIfDirty.bind(Z);Ce.i=b,Ce.id=b.uid,Z.scheduler=()=>wi(Ce),an(b,!0),{}.NODE_ENV!=="production"&&(Z.onTrack=b.rtc?re=>On(b.rtc,re):void 0,Z.onTrigger=b.rtg?re=>On(b.rtg,re):void 0),z()},Ee=(b,C,P)=>{C.component=b;const F=b.vnode.props;b.vnode=C,b.next=null,M_(b,C.props,F,P),j_(b,C.children,P),tr(),vd(b),sr()},Ut=(b,C,P,F,j,q,ee,K,Z=!1)=>{const z=b&&b.children,Ce=b?b.shapeFlag:0,re=C.children,{patchFlag:_e,shapeFlag:De}=C;if(_e>0){if(_e&128){yt(z,re,P,F,j,q,ee,K,Z);return}else if(_e&256){Xt(z,re,P,F,j,q,ee,K,Z);return}}De&8?(Ce&16&&R(z,j,q),re!==z&&p(P,re)):Ce&16?De&16?yt(z,re,P,F,j,q,ee,K,Z):R(z,j,q,!0):(Ce&8&&p(P,""),De&16&&le(re,P,F,j,q,ee,K,Z))},Xt=(b,C,P,F,j,q,ee,K,Z)=>{b=b||xn,C=C||xn;const z=b.length,Ce=C.length,re=Math.min(z,Ce);let _e;for(_e=0;_e<re;_e++){const De=C[_e]=Z?Mr(C[_e]):Vs(C[_e]);U(b[_e],De,P,null,j,q,ee,K,Z)}z>Ce?R(b,j,q,!0,!1,re):le(C,P,F,j,q,ee,K,Z,re)},yt=(b,C,P,F,j,q,ee,K,Z)=>{let z=0;const Ce=C.length;let re=b.length-1,_e=Ce-1;for(;z<=re&&z<=_e;){const De=b[z],Ue=C[z]=Z?Mr(C[z]):Vs(C[z]);if(un(De,Ue))U(De,Ue,P,null,j,q,ee,K,Z);else break;z++}for(;z<=re&&z<=_e;){const De=b[re],Ue=C[_e]=Z?Mr(C[_e]):Vs(C[_e]);if(un(De,Ue))U(De,Ue,P,null,j,q,ee,K,Z);else break;re--,_e--}if(z>re){if(z<=_e){const De=_e+1,Ue=De<Ce?C[De].el:F;for(;z<=_e;)U(null,C[z]=Z?Mr(C[z]):Vs(C[z]),P,Ue,j,q,ee,K,Z),z++}}else if(z>_e)for(;z<=re;)Ke(b[z],j,q,!0),z++;else{const De=z,Ue=z,st=new Map;for(z=Ue;z<=_e;z++){const Tt=C[z]=Z?Mr(C[z]):Vs(C[z]);Tt.key!=null&&({}.NODE_ENV!=="production"&&st.has(Tt.key)&&Y("Duplicate keys found during update:",JSON.stringify(Tt.key),"Make sure keys are unique."),st.set(Tt.key,z))}let Je,Lt=0;const St=_e-Ue+1;let es=!1,Bt=0;const gr=new Array(St);for(z=0;z<St;z++)gr[z]=0;for(z=De;z<=re;z++){const Tt=b[z];if(Lt>=St){Ke(Tt,j,q,!0);continue}let Es;if(Tt.key!=null)Es=st.get(Tt.key);else for(Je=Ue;Je<=_e;Je++)if(gr[Je-Ue]===0&&un(Tt,C[Je])){Es=Je;break}Es===void 0?Ke(Tt,j,q,!0):(gr[Es-Ue]=z+1,Es>=Bt?Bt=Es:es=!0,U(Tt,C[Es],P,null,j,q,ee,K,Z),Lt++)}const $n=es?G_(gr):xn;for(Je=$n.length-1,z=St-1;z>=0;z--){const Tt=Ue+z,Es=C[Tt],aa=Tt+1<Ce?C[Tt+1].el:F;gr[z]===0?U(null,Es,P,aa,j,q,ee,K,Z):es&&(Je<0||z!==$n[Je]?de(Es,P,aa,2):Je--)}}},de=(b,C,P,F,j=null)=>{const{el:q,type:ee,transition:K,children:Z,shapeFlag:z}=b;if(z&6){de(b.component.subTree,C,P,F);return}if(z&128){b.suspense.move(C,P,F);return}if(z&64){ee.move(b,C,P,Re);return}if(ee===Me){i(q,C,P);for(let re=0;re<Z.length;re++)de(Z[re],C,P,F);i(b.anchor,C,P);return}if(ee===Co){we(b,C,P);return}if(F!==2&&z&1&&K)if(F===0)K.beforeEnter(q),i(q,C,P),Jt(()=>K.enter(q),j);else{const{leave:re,delayLeave:_e,afterLeave:De}=K,Ue=()=>i(q,C,P),st=()=>{re(q,()=>{Ue(),De&&De()})};_e?_e(q,Ue,st):st()}else i(q,C,P)},Ke=(b,C,P,F=!1,j=!1)=>{const{type:q,props:ee,ref:K,children:Z,dynamicChildren:z,shapeFlag:Ce,patchFlag:re,dirs:_e,cacheIndex:De}=b;if(re===-2&&(j=!1),K!=null&&Ii(K,null,P,b,!0),De!=null&&(C.renderCache[De]=void 0),Ce&256){C.ctx.deactivate(b);return}const Ue=Ce&1&&_e,st=!Mn(b);let Je;if(st&&(Je=ee&&ee.onVnodeBeforeUnmount)&&Ys(Je,C,b),Ce&6)hs(b.component,P,F);else{if(Ce&128){b.suspense.unmount(P,F);return}Ue&&rn(b,null,C,"beforeUnmount"),Ce&64?b.type.remove(b,C,P,Re,F):z&&!z.hasOnce&&(q!==Me||re>0&&re&64)?R(z,C,P,!1,!0):(q===Me&&re&384||!j&&Ce&16)&&R(Z,C,P),F&&fs(b)}(st&&(Je=ee&&ee.onVnodeUnmounted)||Ue)&&Jt(()=>{Je&&Ys(Je,C,b),Ue&&rn(b,null,C,"unmounted")},P)},fs=b=>{const{type:C,el:P,anchor:F,transition:j}=b;if(C===Me){({}).NODE_ENV!=="production"&&b.patchFlag>0&&b.patchFlag&2048&&j&&!j.persisted?b.children.forEach(ee=>{ee.type===wt?n(ee.el):fs(ee)}):zt(P,F);return}if(C===Co){Q(b);return}const q=()=>{n(P),j&&!j.persisted&&j.afterLeave&&j.afterLeave()};if(b.shapeFlag&1&&j&&!j.persisted){const{leave:ee,delayLeave:K}=j,Z=()=>ee(P,q);K?K(b.el,q,Z):Z()}else q()},zt=(b,C)=>{let P;for(;b!==C;)P=w(b),n(b),b=P;n(C)},hs=(b,C,P)=>{({}).NODE_ENV!=="production"&&b.type.__hmrId&&Hv(b);const{bum:F,scope:j,job:q,subTree:ee,um:K,m:Z,a:z}=b;gf(Z),gf(z),F&&On(F),j.stop(),q&&(q.flags|=8,Ke(ee,b,C,P)),K&&Jt(K,C),Jt(()=>{b.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&Qv(b)},R=(b,C,P,F=!1,j=!1,q=0)=>{for(let ee=q;ee<b.length;ee++)Ke(b[ee],C,P,F,j)},oe=b=>{if(b.shapeFlag&6)return oe(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const C=w(b.anchor||b.el),P=C&&C[Od];return P?w(P):C};let ne=!1;const me=(b,C,P)=>{b==null?C._vnode&&Ke(C._vnode,null,null,!0):U(C._vnode||null,b,C,null,null,null,P),C._vnode=b,ne||(ne=!0,vd(),_d(),ne=!1)},Re={p:U,um:Ke,m:de,r:fs,mt:$e,mc:le,pc:Ut,pbc:ke,n:oe,o:e};let at,Ve;return t&&([at,Ve]=t(Re)),{render:me,hydrate:at,createApp:N_(me,at)}}function Rl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function an({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function W_(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function wo(e,t,s=!1){const i=e.children,n=t.children;if(ge(i)&&ge(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Mr(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&wo(u,c)),c.type===Eo&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.type===wt&&!c.el&&(c.el=u.el)}}function G_(e){const t=e.slice(),s=[0];let i,n,a,u,c;const h=e.length;for(i=0;i<h;i++){const m=e[i];if(m!==0){if(n=s[s.length-1],e[n]<m){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<m?a=c+1:u=c;m<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function mf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:mf(t)}function gf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const K_=Symbol.for("v-scx"),Y_=()=>{{const e=Rs(K_);return e||{}.NODE_ENV!=="production"&&Y("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Q_(e,t){return Vl(e,null,t)}function kn(e,t,s){return{}.NODE_ENV!=="production"&&!Se(t)&&Y("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Vl(e,t,s)}function Vl(e,t,s=nt){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Y('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&Y('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Y('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=pt({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Y);const h=t&&i||!t&&a!=="post";let m;if(Oo){if(a==="sync"){const D=Y_();m=D.__watcherHandles||(D.__watcherHandles=[])}else if(!h){const D=()=>{};return D.stop=Ot,D.resume=Ot,D.pause=Ot,D}}const p=Nt;c.call=(D,k,U)=>Ps(D,p,k,U);let v=!1;a==="post"?c.scheduler=D=>{Jt(D,p&&p.suspense)}:a!=="sync"&&(v=!0,c.scheduler=(D,k)=>{k?D():wi(D)}),c.augmentJob=D=>{t&&(D.flags|=4),v&&(D.flags|=2,p&&(D.id=p.uid,D.i=p))};const w=Pv(e,t,c);return Oo&&(m?m.push(w):h&&w()),w}function Z_(e,t,s){const i=this.proxy,n=ft(e)?e.includes(".")?vf(i,e):()=>i[e]:e.bind(i,i);let a;Se(t)?a=t:(a=t.handler,s=t);const u=So(this),c=Vl(n,a.bind(i),s);return u(),c}function vf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const J_=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${Sr(t)}Modifiers`];function X_(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||nt;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[v]}=e;if(p)if(!(t in p))(!v||!(Zr(Kt(t))in v))&&Y(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Zr(Kt(t))}" prop.`);else{const w=p[t];Se(w)&&(w(...s)||Y(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&J_(i,t.slice(7));if(u&&(u.trim&&(n=s.map(p=>ft(p)?p.trim():p)),u.number&&(n=s.map(li))),{}.NODE_ENV!=="production"&&Xv(e,t,n),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Zr(p)]&&Y(`Event "${p}" is emitted in component ${Li(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Sr(t)}" instead of "${t}".`)}let c,h=i[c=Zr(t)]||i[c=Zr(Kt(t))];!h&&a&&(h=i[c=Zr(Sr(t))]),h&&Ps(h,e,6,n);const m=i[c+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ps(m,e,6,n)}}function _f(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!Se(e)){const h=m=>{const p=_f(m,t,!0);p&&(c=!0,pt(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Xe(e)&&i.set(e,null),null):(ge(a)?a.forEach(h=>u[h]=null):pt(u,a),Xe(e)&&i.set(e,u),u)}function Pi(e,t){return!e||!so(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ze(e,t[0].toLowerCase()+t.slice(1))||Ze(e,Sr(t))||Ze(e,t))}let Fl=!1;function ki(){Fl=!0}function Ul(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:h,render:m,renderCache:p,props:v,data:w,setupState:D,ctx:k,inheritAttrs:U}=e,te=Di(e);let T,se;({}).NODE_ENV!=="production"&&(Fl=!1);try{if(s.shapeFlag&4){const Q=n||i,he={}.NODE_ENV!=="production"&&D.__isScriptSetup?new Proxy(Q,{get(be,Ae,le){return Y(`Property '${String(Ae)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(be,Ae,le)}}):Q;T=Vs(m.call(he,Q,p,{}.NODE_ENV!=="production"?zs(v):v,D,w,k)),se=c}else{const Q=t;({}).NODE_ENV!=="production"&&c===v&&ki(),T=Vs(Q.length>1?Q({}.NODE_ENV!=="production"?zs(v):v,{}.NODE_ENV!=="production"?{get attrs(){return ki(),zs(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):Q({}.NODE_ENV!=="production"?zs(v):v,null)),se=t.props?c:ey(c)}}catch(Q){Do.length=0,ho(Q,e,1),T=A(wt)}let G=T,we;if({}.NODE_ENV!=="production"&&T.patchFlag>0&&T.patchFlag&2048&&([G,we]=yf(T)),se&&U!==!1){const Q=Object.keys(se),{shapeFlag:he}=G;if(Q.length){if(he&7)a&&Q.some(oi)&&(se=ty(se,a)),G=Ks(G,se,!1,!0);else if({}.NODE_ENV!=="production"&&!Fl&&G.type!==wt){const be=Object.keys(c),Ae=[],le=[];for(let ie=0,ke=be.length;ie<ke;ie++){const ae=be[ie];so(ae)?oi(ae)||Ae.push(ae[2].toLowerCase()+ae.slice(3)):le.push(ae)}le.length&&Y(`Extraneous non-props attributes (${le.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ae.length&&Y(`Extraneous non-emits event listeners (${Ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!bf(G)&&Y("Runtime directive used on component with non-element root node. The directives will not function as intended."),G=Ks(G,null,!1,!0),G.dirs=G.dirs?G.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!bf(G)&&Y("Component inside <Transition> renders non-element root node that cannot be animated."),vo(G,s.transition)),{}.NODE_ENV!=="production"&&we?we(G):T=G,Di(te),T}const yf=e=>{const t=e.children,s=e.dynamicChildren,i=Ll(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return yf(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Vs(i),u]};function Ll(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(ln(n)){if(n.type!==wt||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Ll(s.children)}}else return}return s}const ey=e=>{let t;for(const s in e)(s==="class"||s==="style"||so(s))&&((t||(t={}))[s]=e[s]);return t},ty=(e,t)=>{const s={};for(const i in e)(!oi(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},bf=e=>e.shapeFlag&7||e.type===wt;function sy(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:h}=t,m=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&ks||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?wf(i,u,m):!!u;if(h&8){const p=t.dynamicProps;for(let v=0;v<p.length;v++){const w=p[v];if(u[w]!==i[w]&&!Pi(m,w))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?wf(i,u,m):!0:!!u;return!1}function wf(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!Pi(s,a))return!0}return!1}function ry({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const Ef=e=>e.__isSuspense;function ny(e,t){t&&t.pendingBranch?ge(e)?t.effects.push(...e):t.effects.push(e):gd(e)}const Me=Symbol.for("v-fgt"),Eo=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Co=Symbol.for("v-stc"),Do=[];let cs=null;function O(e=!1){Do.push(cs=e?null:[])}function oy(){Do.pop(),cs=Do[Do.length-1]||null}let xo=1;function Cf(e,t=!1){xo+=e,e<0&&cs&&t&&(cs.hasOnce=!0)}function Df(e){return e.dynamicChildren=xo>0?cs||xn:null,oy(),xo>0&&cs&&cs.push(e),e}function N(e,t,s,i,n,a){return Df(f(e,t,s,i,n,a,!0))}function Pt(e,t,s,i,n){return Df(A(e,t,s,i,n,!0))}function ln(e){return e?e.__v_isVNode===!0:!1}function un(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=Ei.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const iy=(...e)=>Sf(...e),xf=({key:e})=>e??null,Ri=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ft(e)||Dt(e)||Se(e)?{i:xt,r:e,k:t,f:!!s}:e:null);function f(e,t=null,s=null,i=0,n=null,a=e===Me?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&xf(t),ref:t&&Ri(t),scopeId:xd,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:xt};return c?(Bl(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=ft(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Y("VNode created with invalid key (NaN). VNode type:",h.type),xo>0&&!u&&cs&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&cs.push(h),h}const A={}.NODE_ENV!=="production"?iy:Sf;function Sf(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===v_)&&({}.NODE_ENV!=="production"&&!e&&Y(`Invalid vnode type when creating vnode: ${e}.`),e=wt),ln(e)){const c=Ks(e,t,!0);return s&&Bl(c,s),xo>0&&!a&&cs&&(c.shapeFlag&6?cs[cs.indexOf(e)]=c:cs.push(c)),c.patchFlag=-2,c}if(Pf(e)&&(e=e.__vccOpts),t){t=ay(t);let{class:c,style:h}=t;c&&!ft(c)&&(t.class=pe(c)),Xe(h)&&(mi(h)&&!ge(h)&&(h=pt({},h)),t.style=ls(h))}const u=ft(e)?1:Ef(e)?128:Id(e)?64:Xe(e)?4:Se(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&mi(e)&&(e=Te(e),Y("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),f(e,t,s,i,n,u,a,!0)}function ay(e){return e?mi(e)||nf(e)?pt({},e):e:null}function Ks(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:h}=e,m=t?uy(n||{},t):n,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&xf(m),ref:t&&t.ref?s&&a?ge(a)?a.concat(Ri(t)):[a,Ri(t)]:Ri(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&ge(c)?c.map(Of):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Me?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ks(e.ssContent),ssFallback:e.ssFallback&&Ks(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&vo(p,h.clone(p)),p}function Of(e){const t=Ks(e);return ge(e.children)&&(t.children=e.children.map(Of)),t}function tt(e=" ",t=0){return A(Eo,null,e,t)}function ly(e,t){const s=A(Co,null,e);return s.staticCount=t,s}function ce(e="",t=!1){return t?(O(),Pt(wt,null,e)):A(wt,null,e)}function Vs(e){return e==null||typeof e=="boolean"?A(wt):ge(e)?A(Me,null,e.slice()):ln(e)?Mr(e):A(Eo,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ks(e)}function Bl(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(ge(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),Bl(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!nf(t)?t._ctx=xt:n===3&&xt&&(xt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Se(t)?(t={default:t,_ctx:xt},s=32):(t=String(t),i&64?(s=16,t=[tt(t)]):s=8);e.children=t,e.shapeFlag|=s}function uy(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=pe([t.class,i.class]));else if(n==="style")t.style=ls([t.style,i.style]);else if(so(n)){const a=t[n],u=i[n];u&&a!==u&&!(ge(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Ys(e,t,s,i=null){Ps(e,t,7,[s,i])}const cy=tf();let dy=0;function fy(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||cy,a={uid:dy++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new jc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:af(i,n),emitsOptions:_f(i,n),emit:null,emitted:null,propsDefaults:nt,inheritAttrs:i.inheritAttrs,ctx:nt,data:nt,props:nt,attrs:nt,slots:nt,refs:nt,setupState:nt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=y_(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=X_.bind(null,a),e.ce&&e.ce(a),a}let Nt=null;const Vi=()=>Nt||xt;let Fi,$l;{const e=oo(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};Fi=t("__VUE_INSTANCE_SETTERS__",s=>Nt=s),$l=t("__VUE_SSR_SETTERS__",s=>Oo=s)}const So=e=>{const t=Nt;return Fi(e),e.scope.on(),()=>{e.scope.off(),Fi(t)}},If=()=>{Nt&&Nt.scope.off(),Fi(null)},hy=er("slot,component");function jl(e,{isNativeTag:t}){(hy(e)||t(e))&&Y("Do not use built-in or reserved HTML elements as component id: "+e)}function Nf(e){return e.vnode.shapeFlag&4}let Oo=!1;function py(e,t=!1,s=!1){t&&$l(t);const{props:i,children:n}=e.vnode,a=Nf(e);T_(e,i,a,t),$_(e,n,s);const u=a?my(e,t):void 0;return t&&$l(!1),u}function my(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&jl(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)jl(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Sd(a[u])}i.compilerOptions&&gy()&&Y('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Yd),{}.NODE_ENV!=="production"&&b_(e);const{setup:n}=i;if(n){tr();const a=e.setupContext=n.length>1?_y(e):null,u=So(e),c=Nn(n,e,0,[{}.NODE_ENV!=="production"?zs(e.props):e.props,a]),h=Za(c);if(sr(),u(),(h||e.sp)&&!Mn(e)&&$d(e),h){if(c.then(If,If),t)return c.then(m=>{Tf(e,m,t)}).catch(m=>{ho(m,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const m=(s=i.name)!=null?s:"Anonymous";Y(`Component <${m}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Tf(e,c,t)}else Af(e,t)}function Tf(e,t,s){Se(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Xe(t)?({}.NODE_ENV!=="production"&&ln(t)&&Y("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=fd(t),{}.NODE_ENV!=="production"&&w_(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Y(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Af(e,s)}let Hl;const gy=()=>!Hl;function Af(e,t,s){const i=e.type;if(!e.render){if(!t&&Hl&&!i.render){const n=i.template||Tl(e).template;if(n){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,m=pt(pt({isCustomElement:a,delimiters:c},u),h);i.render=Hl(n,m),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||Ot}{const n=So(e);tr();try{C_(e)}finally{sr(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Ot&&!t&&(i.template?Y('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Y("Component is missing template or render function: ",i))}const Mf={}.NODE_ENV!=="production"?{get(e,t){return ki(),It(e,"get",""),e[t]},set(){return Y("setupContext.attrs is readonly."),!1},deleteProperty(){return Y("setupContext.attrs is readonly."),!1}}:{get(e,t){return It(e,"get",""),e[t]}};function vy(e){return new Proxy(e.slots,{get(t,s){return It(e,"get","$slots"),t[s]}})}function _y(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Y("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(ge(s)?i="array":Dt(s)&&(i="ref")),i!=="object"&&Y(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Mf))},get slots(){return i||(i=vy(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Mf),slots:e.slots,emit:e.emit,expose:t}}function Ui(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fd(hl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in on)return on[s](e)},has(t,s){return s in t||s in on}})):e.proxy}const yy=/(?:^|[-_])(\w)/g,by=e=>e.replace(yy,t=>t.toUpperCase()).replace(/[-_]/g,"");function ql(e,t=!0){return Se(e)?e.displayName||e.name:e.name||t&&e.__name}function Li(e,t,s=!1){let i=ql(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?by(i):s?"App":"Anonymous"}function Pf(e){return Se(e)&&"__vccOpts"in e}const Fs=(e,t)=>{const s=Av(e,t,Oo);if({}.NODE_ENV!=="production"){const i=Vi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function zl(e,t,s){const i=arguments.length;return i===2?Xe(t)&&!ge(t)?ln(t)?A(e,null,[t]):A(e,t):A(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&ln(s)&&(s=[s]),A(e,t,s))}function wy(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(v){return Xe(v)?v.__isVue?["div",e,"VueInstance"]:Dt(v)?["div",{},["span",e,p(v)],"<",c("_value"in v?v._value:v),">"]:Xr(v)?["div",{},["span",e,Yt(v)?"ShallowReactive":"Reactive"],"<",c(v),`>${nr(v)?" (readonly)":""}`]:nr(v)?["div",{},["span",e,Yt(v)?"ShallowReadonly":"Readonly"],"<",c(v),">"]:null:null},hasBody(v){return v&&v.__isVue},body(v){if(v&&v.__isVue)return["div",{},...a(v.$)]}};function a(v){const w=[];v.type.props&&v.props&&w.push(u("props",Te(v.props))),v.setupState!==nt&&w.push(u("setup",v.setupState)),v.data!==nt&&w.push(u("data",Te(v.data)));const D=h(v,"computed");D&&w.push(u("computed",D));const k=h(v,"inject");return k&&w.push(u("injected",k)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:v}]]),w}function u(v,w){return w=pt({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},v],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(D=>["div",{},["span",i,D+": "],c(w[D],!1)])]]:["span",{}]}function c(v,w=!0){return typeof v=="number"?["span",t,v]:typeof v=="string"?["span",s,JSON.stringify(v)]:typeof v=="boolean"?["span",i,v]:Xe(v)?["object",{object:w?Te(v):v}]:["span",s,String(v)]}function h(v,w){const D=v.type;if(Se(D))return;const k={};for(const U in v.ctx)m(D,U,w)&&(k[U]=v.ctx[U]);return k}function m(v,w,D){const k=v[D];if(ge(k)&&k.includes(w)||Xe(k)&&w in k||v.extends&&m(v.extends,w,D)||v.mixins&&v.mixins.some(U=>m(U,w,D)))return!0}function p(v){return Yt(v)?"ShallowRef":v.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const kf="3.5.13",Qs={}.NODE_ENV!=="production"?Y:Ot;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Wl;const Rf=typeof window<"u"&&window.trustedTypes;if(Rf)try{Wl=Rf.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Qs(`Error creating trusted types policy: ${e}`)}const Vf=Wl?e=>Wl.createHTML(e):e=>e,Ey="http://www.w3.org/2000/svg",Cy="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,Ff=ur&&ur.createElement("template"),Dy={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?ur.createElementNS(Ey,e):t==="mathml"?ur.createElementNS(Cy,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{Ff.innerHTML=Vf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=Ff.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",Io="animation",No=Symbol("_vtc"),Uf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},xy=pt({},kd,Uf),Lf=(e=>(e.displayName="Transition",e.props=xy,e))((e,{slots:t})=>zl(n_,Sy(e),t)),cn=(e,t=[])=>{ge(e)?e.forEach(s=>s(...t)):e&&e(...t)},Bf=e=>e?ge(e)?e.some(t=>t.length>1):e.length>1:!1;function Sy(e){const t={};for(const ae in e)ae in Uf||(t[ae]=e[ae]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:m=u,appearToClass:p=c,leaveFromClass:v=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:D=`${s}-leave-to`}=e,k=Oy(n),U=k&&k[0],te=k&&k[1],{onBeforeEnter:T,onEnter:se,onEnterCancelled:G,onLeave:we,onLeaveCancelled:Q,onBeforeAppear:he=T,onAppear:be=se,onAppearCancelled:Ae=G}=t,le=(ae,We,J,$e)=>{ae._enterCancelled=$e,dn(ae,We?p:c),dn(ae,We?m:u),J&&J()},ie=(ae,We)=>{ae._isLeaving=!1,dn(ae,v),dn(ae,D),dn(ae,w),We&&We()},ke=ae=>(We,J)=>{const $e=ae?be:se,ut=()=>le(We,ae,J);cn($e,[We,ut]),$f(()=>{dn(We,ae?h:a),cr(We,ae?p:c),Bf($e)||jf(We,i,U,ut)})};return pt(t,{onBeforeEnter(ae){cn(T,[ae]),cr(ae,a),cr(ae,u)},onBeforeAppear(ae){cn(he,[ae]),cr(ae,h),cr(ae,m)},onEnter:ke(!1),onAppear:ke(!0),onLeave(ae,We){ae._isLeaving=!0;const J=()=>ie(ae,We);cr(ae,v),ae._enterCancelled?(cr(ae,w),zf()):(zf(),cr(ae,w)),$f(()=>{ae._isLeaving&&(dn(ae,v),cr(ae,D),Bf(we)||jf(ae,i,te,J))}),cn(we,[ae,J])},onEnterCancelled(ae){le(ae,!1,void 0,!0),cn(G,[ae])},onAppearCancelled(ae){le(ae,!0,void 0,!0),cn(Ae,[ae])},onLeaveCancelled(ae){ie(ae),cn(Q,[ae])}})}function Oy(e){if(e==null)return null;if(Xe(e))return[Gl(e.enter),Gl(e.leave)];{const t=Gl(e);return[t,t]}}function Gl(e){const t=Kg(e);return{}.NODE_ENV!=="production"&&Uv(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[No]||(e[No]=new Set)).add(t)}function dn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[No];s&&(s.delete(t),s.size||(e[No]=void 0))}function $f(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Iy=0;function jf(e,t,s,i){const n=e._endId=++Iy,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=Ny(e,t);if(!u)return i();const m=u+"end";let p=0;const v=()=>{e.removeEventListener(m,w),a()},w=D=>{D.target===e&&++p>=h&&v()};setTimeout(()=>{p<h&&v()},c+1),e.addEventListener(m,w)}function Ny(e,t){const s=window.getComputedStyle(e),i=k=>(s[k]||"").split(", "),n=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=Hf(n,a),c=i(`${Io}Delay`),h=i(`${Io}Duration`),m=Hf(c,h);let p=null,v=0,w=0;t===Pr?u>0&&(p=Pr,v=u,w=a.length):t===Io?m>0&&(p=Io,v=m,w=h.length):(v=Math.max(u,m),p=v>0?u>m?Pr:Io:null,w=p?p===Pr?a.length:h.length:0);const D=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:v,propCount:w,hasTransform:D}}function Hf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>qf(s)+qf(e[i])))}function qf(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function zf(){return document.body.offsetHeight}function Ty(e,t,s){const i=e[No];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Bi=Symbol("_vod"),Wf=Symbol("_vsh"),Kl={beforeMount(e,{value:t},{transition:s}){e[Bi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):To(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),To(e,!0),i.enter(e)):i.leave(e,()=>{To(e,!1)}):To(e,t))},beforeUnmount(e,{value:t}){To(e,t)}};({}).NODE_ENV!=="production"&&(Kl.name="show");function To(e,t){e.style.display=t?e[Bi]:"none",e[Wf]=!t}const Ay=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),My=/(^|;)\s*display\s*:/;function Py(e,t,s){const i=e.style,n=ft(s);let a=!1;if(s&&!n){if(t)if(ft(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&$i(i,c,"")}else for(const u in t)s[u]==null&&$i(i,u,"");for(const u in s)u==="display"&&(a=!0),$i(i,u,s[u])}else if(n){if(t!==s){const u=i[Ay];u&&(s+=";"+u),i.cssText=s,a=My.test(s)}}else t&&e.removeAttribute("style");Bi in e&&(e[Bi]=a?i.display:"",e[Wf]&&(i.display="none"))}const ky=/[^\\];\s*$/,Gf=/\s*!important$/;function $i(e,t,s){if(ge(s))s.forEach(i=>$i(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&ky.test(s)&&Qs(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Ry(e,t);Gf.test(s)?e.setProperty(Sr(i),s.replace(Gf,""),"important"):e[i]=s}}const Kf=["Webkit","Moz","ms"],Yl={};function Ry(e,t){const s=Yl[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return Yl[t]=i;i=Qr(i);for(let n=0;n<Kf.length;n++){const a=Kf[n]+i;if(a in e)return Yl[t]=a}return t}const Yf="http://www.w3.org/1999/xlink";function Qf(e,t,s,i,n,a=ov(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Yf,t.slice(6,t.length)):e.setAttributeNS(Yf,t,s):s==null||a&&!Lc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":As(s)?String(s):s)}function Zf(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Vf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Lc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Qs(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function Vy(e,t,s,i){e.removeEventListener(t,s,i)}const Jf=Symbol("_vei");function Fy(e,t,s,i,n=null){const a=e[Jf]||(e[Jf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?eh(i,t):i;else{const[c,h]=Uy(t);if(i){const m=a[t]=$y({}.NODE_ENV!=="production"?eh(i,t):i,n);kr(e,c,m,h)}else u&&(Vy(e,c,u,h),a[t]=void 0)}}const Xf=/(?:Once|Passive|Capture)$/;function Uy(e){let t;if(Xf.test(e)){t={};let i;for(;i=e.match(Xf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Sr(e.slice(2)),t]}let Ql=0;const Ly=Promise.resolve(),By=()=>Ql||(Ly.then(()=>Ql=0),Ql=Date.now());function $y(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ps(jy(i,s.value),t,5,[i])};return s.value=e,s.attached=By(),s}function eh(e,t){return Se(e)||ge(e)?e:(Qs(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Ot)}function jy(e,t){if(ge(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const th=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Hy=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?Ty(e,i,u):t==="style"?Py(e,s,i):so(t)?oi(t)||Fy(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):qy(e,t,i,u))?(Zf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Qf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ft(i))?Zf(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Qf(e,t,i,u))};function qy(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&th(t)&&Se(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return th(t)&&ft(s)?!1:t in e}const Rn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ge(t)?s=>On(t,s):t};function zy(e){e.target.composing=!0}function sh(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),ws={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[dr]=Rn(n);const a=i||n.props&&n.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=li(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",zy),kr(e,"compositionend",sh),kr(e,"change",sh))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[dr]=Rn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?li(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===h)||(e.value=h))}},ji={deep:!0,created(e,t,s){e[dr]=Rn(s),kr(e,"change",()=>{const i=e._modelValue,n=Ao(e),a=e.checked,u=e[dr];if(ge(i)){const c=el(i,n),h=c!==-1;if(a&&!h)u(i.concat(n));else if(!a&&h){const m=[...i];m.splice(c,1),u(m)}}else if(Sn(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(oh(e,a))})},mounted:rh,beforeUpdate(e,t,s){e[dr]=Rn(s),rh(e,t,s)}};function rh(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(ge(t))n=el(t,i.props.value)>-1;else if(Sn(t))n=t.has(i.props.value);else{if(t===s)return;n=io(t,oh(e,!0))}e.checked!==n&&(e.checked=n)}const Zl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=Sn(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?li(Ao(u)):Ao(u));e[dr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,vl(()=>{e._assigning=!1})}),e[dr]=Rn(i)},mounted(e,{value:t}){nh(e,t)},beforeUpdate(e,t,s){e[dr]=Rn(s)},updated(e,{value:t}){e._assigning||nh(e,t)}};function nh(e,t){const s=e.multiple,i=ge(t);if(s&&!i&&!Sn(t)){({}).NODE_ENV!=="production"&&Qs(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Ao(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(m=>String(m)===String(c)):u.selected=el(t,c)>-1}else u.selected=t.has(c);else if(io(Ao(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Ao(e){return"_value"in e?e._value:e.value}function oh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Wy=["ctrl","shift","alt","meta"],Gy={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Wy.some(s=>e[`${s}Key`]&&!t.includes(s))},Ft=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=Gy[t[u]];if(c&&c(n,t))return}return e(n,...a)})},Ky=pt({patchProp:Hy},Dy);let ih;function Yy(){return ih||(ih=q_(Ky))}const Qy=(...e)=>{const t=Yy().createApp(...e);({}).NODE_ENV!=="production"&&(Jy(t),Xy(t));const{mount:s}=t;return t.mount=i=>{const n=eb(i);if(!n)return;const a=t._component;!Se(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,Zy(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function Zy(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Jy(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>sv(t)||rv(t)||nv(t),writable:!1})}function Xy(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Qs("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Qs(i),s},set(){Qs(i)}})}}function eb(e){if(ft(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Qs(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Qs('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function tb(){wy()}({}).NODE_ENV!=="production"&&tb();var sb=!1;function rb(){return ah().__VUE_DEVTOOLS_GLOBAL_HOOK__}function ah(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const nb=typeof Proxy=="function",ob="devtools-plugin:setup",ib="plugin:settings:set";let Vn,Jl;function ab(){var e;return Vn!==void 0||(typeof window<"u"&&window.performance?(Vn=!0,Jl=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Vn=!0,Jl=globalThis.perf_hooks.performance):Vn=!1),Vn}function lb(){return ab()?Jl.now():Date.now()}class ub{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return lb()}},s&&s.on(ib,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(m=>{this.targetQueue.push({method:c,args:h,resolve:m})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Xl(e,t){const s=e,i=ah(),n=rb(),a=nb&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(ob,e,t);else{const u=a?new ub(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const cb={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var fn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(fn||(fn={}));const eu=typeof window<"u",lh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function db(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function tu(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){dh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function uh(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function Hi(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const qi=typeof navigator=="object"?navigator:{userAgent:""},ch=(()=>/Macintosh/.test(qi.userAgent)&&/AppleWebKit/.test(qi.userAgent)&&!/Safari/.test(qi.userAgent))(),dh=eu?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ch?fb:"msSaveOrOpenBlob"in qi?hb:pb:()=>{};function fb(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?uh(i.href)?tu(e,t,s):(i.target="_blank",Hi(i)):Hi(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){Hi(i)},0))}function hb(e,t="download",s){if(typeof e=="string")if(uh(e))tu(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){Hi(i)})}else navigator.msSaveOrOpenBlob(db(e,s),t)}function pb(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return tu(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(lh.HTMLElement))||"safari"in lh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||ch)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function kt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function su(e){return"_a"in e&&"install"in e}function fh(){if(!("clipboard"in navigator))return kt("Your browser doesn't support the Clipboard API","error"),!0}function hh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(kt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function mb(e){if(!fh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),kt("Global state copied to clipboard.")}catch(t){if(hh(t))return;kt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function gb(e){if(!fh())try{ph(e,JSON.parse(await navigator.clipboard.readText())),kt("Global state pasted from clipboard.")}catch(t){if(hh(t))return;kt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function vb(e){try{dh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){kt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function _b(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function yb(e){try{const s=await _b()();if(!s)return;const{text:i,file:n}=s;ph(e,JSON.parse(i)),kt(`Global state imported from "${n.name}".`)}catch(t){kt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function ph(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Us(e){return{_custom:{display:e}}}const mh="🍍 Pinia (root)",zi="_root";function bb(e){return su(e)?{id:zi,label:mh}:{id:e.$id,label:e.$id}}function wb(e){if(su(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function Eb(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Us(e.type),key:Us(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function Cb(e){switch(e){case fn.direct:return"mutation";case fn.patchFunction:return"$patch";case fn.patchObject:return"$patch";default:return"unknown"}}let Fn=!0;const Wi=[],hn="pinia:mutations",qt="pinia",{assign:Db}=Object,Gi=e=>"🍍 "+e;function xb(e,t){Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Wi,app:e},s=>{typeof s.now!="function"&&kt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:hn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:qt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{mb(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await gb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{vb(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await yb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?kt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),kt(`Store "${i}" reset.`)):kt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Gi(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Te(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,m)=>(h[m]=c.$state[m],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Gi(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,m)=>{try{h[m]=c[m]}catch(p){h[m]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===qt){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):mh.toLowerCase().includes(i.filter.toLowerCase())):n).map(bb)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===qt){const n=i.nodeId===zi?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==zi&&(globalThis.$store=Te(n)),i.state=wb(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===qt){const a=i.nodeId===zi?t:t._s.get(i.nodeId);if(!a)return kt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;su(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Fn=!1,i.set(a,u,i.state.value),Fn=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return kt(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return kt(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Fn=!1,i.set(a,u,i.state.value),Fn=!0}})})}function Sb(e,t){Wi.includes(Gi(t.$id))||Wi.push(Gi(t.$id)),Xl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:Wi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:m})=>{const p=gh++;s.addTimelineEvent({layerId:hn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Us(t.$id),action:Us(h),args:m},groupId:p}}),u(v=>{Rr=void 0,s.addTimelineEvent({layerId:hn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Us(t.$id),action:Us(h),args:m,result:v},groupId:p}})}),c(v=>{Rr=void 0,s.addTimelineEvent({layerId:hn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Us(t.$id),action:Us(h),args:m,error:v},groupId:p}})})},!0),t._customProperties.forEach(u=>{kn(()=>Ir(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(qt),Fn&&s.addTimelineEvent({layerId:hn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Rr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(qt),!Fn)return;const m={time:i(),title:Cb(c),data:Db({store:Us(t.$id)},Eb(u)),groupId:Rr};c===fn.patchFunction?m.subtitle="⤵️":c===fn.patchObject?m.subtitle="🧩":u&&!Array.isArray(u)&&(m.subtitle=u.type),u&&(m.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:hn,event:m})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=hl(u=>{n(u),s.addTimelineEvent({layerId:hn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Us(t.$id),info:Us("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&kt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&kt(`"${t.$id}" store installed 🆕`)})}let gh=0,Rr;function vh(e,t,s){const i=t.reduce((n,a)=>(n[a]=Te(e)[a],n),{});for(const n in i)e[n]=function(){const a=gh,u=s?new Proxy(e,{get(...h){return Rr=a,Reflect.get(...h)},set(...h){return Rr=a,Reflect.set(...h)}}):e;Rr=a;const c=i[n].apply(u,arguments);return Rr=void 0,c}}function Ob({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){vh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Te(t)._hotUpdate=function(n){i.apply(this,arguments),vh(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}Sb(e,t)}}function Ib(){const e=av(!0),t=e.run(()=>cd({}));let s=[],i=[];const n=hl({install(a){n._a=a,a.provide(cb,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&xb(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!sb?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&eu&&typeof Proxy<"u"&&n.use(Ob),n}const KR="",ze=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},Nb={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},Tb={id:"app"};function Ab(e,t,s,i,n,a){const u=X("router-view");return O(),N("div",Tb,[A(u)])}const Mb=ze(Nb,[["render",Ab]]);/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function _h(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Pb(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&_h(e.default)}const et=Object.assign;function ru(e,t){const s={};for(const i in t){const n=t[i];s[i]=ds(n)?n.map(e):e(n)}return s}const Mo=()=>{},ds=Array.isArray;function He(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const yh=/#/g,kb=/&/g,Rb=/\//g,Vb=/=/g,Fb=/\?/g,bh=/\+/g,Ub=/%5B/g,Lb=/%5D/g,wh=/%5E/g,Bb=/%60/g,Eh=/%7B/g,$b=/%7C/g,Ch=/%7D/g,jb=/%20/g;function nu(e){return encodeURI(""+e).replace($b,"|").replace(Ub,"[").replace(Lb,"]")}function Hb(e){return nu(e).replace(Eh,"{").replace(Ch,"}").replace(wh,"^")}function ou(e){return nu(e).replace(bh,"%2B").replace(jb,"+").replace(yh,"%23").replace(kb,"%26").replace(Bb,"`").replace(Eh,"{").replace(Ch,"}").replace(wh,"^")}function qb(e){return ou(e).replace(Vb,"%3D")}function zb(e){return nu(e).replace(yh,"%23").replace(Fb,"%3F")}function Wb(e){return e==null?"":zb(e).replace(Rb,"%2F")}function Un(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&He(`Error decoding "${e}". Using original value`)}return""+e}const Gb=/\/$/,Kb=e=>e.replace(Gb,"");function iu(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Zb(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:Un(u)}}function Yb(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function Dh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function xh(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&Vr(t.matched[i],s.matched[n])&&Sh(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Vr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Sh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Qb(e[s],t[s]))return!1;return!0}function Qb(e,t){return ds(e)?Oh(e,t):ds(t)?Oh(t,e):e===t}function Oh(e,t){return ds(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Zb(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return He(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Fr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Po;(function(e){e.pop="pop",e.push="push"})(Po||(Po={}));var ko;(function(e){e.back="back",e.forward="forward",e.unknown=""})(ko||(ko={}));function Jb(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Kb(e)}const Xb=/^[^#]+#/;function e0(e,t){return e.replace(Xb,"#")+t}function t0(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Ki=()=>({left:window.scrollX,top:window.scrollY});function s0(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){He(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{He(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&He(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=t0(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ih(e,t){return(history.state?history.state.position-t:-1)+e}const au=new Map;function r0(e,t){au.set(e,t)}function n0(e){const t=au.get(e);return au.delete(e),t}let o0=()=>location.protocol+"//"+location.host;function Nh(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,h=n.slice(c);return h[0]!=="/"&&(h="/"+h),Dh(h,"")}return Dh(s,e)+i+n}function i0(e,t,s,i){let n=[],a=[],u=null;const c=({state:w})=>{const D=Nh(e,location),k=s.value,U=t.value;let te=0;if(w){if(s.value=D,t.value=w,u&&u===k){u=null;return}te=U?w.position-U.position:0}else i(D);n.forEach(T=>{T(s.value,k,{delta:te,type:Po.pop,direction:te?te>0?ko.forward:ko.back:ko.unknown})})};function h(){u=s.value}function m(w){n.push(w);const D=()=>{const k=n.indexOf(w);k>-1&&n.splice(k,1)};return a.push(D),D}function p(){const{history:w}=window;w.state&&w.replaceState(et({},w.state,{scroll:Ki()}),"")}function v(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:m,destroy:v}}function Th(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?Ki():null}}function a0(e){const{history:t,location:s}=window,i={value:Nh(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,m,p){const v=e.indexOf("#"),w=v>-1?(s.host&&document.querySelector("base")?e:e.slice(v))+h:o0()+e+h;try{t[p?"replaceState":"pushState"](m,"",w),n.value=m}catch(D){({}).NODE_ENV!=="production"?He("Error with push/replace State",D):console.error(D),s[p?"replace":"assign"](w)}}function u(h,m){const p=et({},t.state,Th(n.value.back,h,n.value.forward,!0),m,{position:n.value.position});a(h,p,!0),i.value=h}function c(h,m){const p=et({},n.value,t.state,{forward:h,scroll:Ki()});({}).NODE_ENV!=="production"&&!t.state&&He(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const v=et({},Th(i.value,h,null),{position:p.position+1},m);a(h,v,!1),i.value=h}return{location:i,state:n,push:c,replace:u}}function l0(e){e=Jb(e);const t=a0(e),s=i0(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=et({location:"",base:e,go:i,createHref:e0.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Yi(e){return typeof e=="string"||e&&typeof e=="object"}function Ah(e){return typeof e=="string"||typeof e=="symbol"}const lu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Mh;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Mh||(Mh={}));const u0={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${d0(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Ln(e,t){return{}.NODE_ENV!=="production"?et(new Error(u0[e](t)),{type:e,[lu]:!0},t):et(new Error,{type:e,[lu]:!0},t)}function pr(e,t){return e instanceof Error&&lu in e&&(t==null||!!(e.type&t))}const c0=["params","query","hash"];function d0(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of c0)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Ph="[^/]+?",f0={sensitive:!1,strict:!1,start:!0,end:!0},h0=/[.+*?^${}()[\]/\\]/g;function p0(e,t){const s=et({},f0,t),i=[];let n=s.start?"^":"";const a=[];for(const m of e){const p=m.length?[]:[90];s.strict&&!m.length&&(n+="/");for(let v=0;v<m.length;v++){const w=m[v];let D=40+(s.sensitive?.25:0);if(w.type===0)v||(n+="/"),n+=w.value.replace(h0,"\\$&"),D+=40;else if(w.type===1){const{value:k,repeatable:U,optional:te,regexp:T}=w;a.push({name:k,repeatable:U,optional:te});const se=T||Ph;if(se!==Ph){D+=10;try{new RegExp(`(${se})`)}catch(we){throw new Error(`Invalid custom RegExp for param "${k}" (${se}): `+we.message)}}let G=U?`((?:${se})(?:/(?:${se}))*)`:`(${se})`;v||(G=te&&m.length<2?`(?:/${G})`:"/"+G),te&&(G+="?"),n+=G,D+=20,te&&(D+=-8),U&&(D+=-20),se===".*"&&(D+=-50)}p.push(D)}i.push(p)}if(s.strict&&s.end){const m=i.length-1;i[m][i[m].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(m){const p=m.match(u),v={};if(!p)return null;for(let w=1;w<p.length;w++){const D=p[w]||"",k=a[w-1];v[k.name]=D&&k.repeatable?D.split("/"):D}return v}function h(m){let p="",v=!1;for(const w of e){(!v||!p.endsWith("/"))&&(p+="/"),v=!1;for(const D of w)if(D.type===0)p+=D.value;else if(D.type===1){const{value:k,repeatable:U,optional:te}=D,T=k in m?m[k]:"";if(ds(T)&&!U)throw new Error(`Provided param "${k}" is an array but it is not repeatable (* or + modifiers)`);const se=ds(T)?T.join("/"):T;if(!se)if(te)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):v=!0);else throw new Error(`Missing required param "${k}"`);p+=se}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function m0(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function kh(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=m0(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(Rh(i))return 1;if(Rh(n))return-1}return n.length-i.length}function Rh(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const g0={type:0,value:""},v0=/[a-zA-Z0-9_]/;function _0(e){if(!e)return[[]];if(e==="/")return[[g0]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(D){throw new Error(`ERR (${s})/"${m}": ${D}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,h,m="",p="";function v(){m&&(s===0?a.push({type:0,value:m}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:m,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),m="")}function w(){m+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(m&&v(),u()):h===":"?(v(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:v0.test(h)?w():(v(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:v(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${m}"`),v(),u(),n}function y0(e,t,s){const i=p0(_0(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&He(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=et(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function b0(e,t){const s=[],i=new Map;t=Lh({strict:!1,end:!0,sensitive:!1},t);function n(v){return i.get(v)}function a(v,w,D){const k=!D,U=Fh(v);({}).NODE_ENV!=="production"&&D0(U,w),U.aliasOf=D&&D.record;const te=Lh(t,v),T=[U];if("alias"in v){const we=typeof v.alias=="string"?[v.alias]:v.alias;for(const Q of we)T.push(Fh(et({},U,{components:D?D.record.components:U.components,path:Q,aliasOf:D?D.record:U})))}let se,G;for(const we of T){const{path:Q}=we;if(w&&Q[0]!=="/"){const he=w.record.path,be=he[he.length-1]==="/"?"":"/";we.path=w.record.path+(Q&&be+Q)}if({}.NODE_ENV!=="production"&&we.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(se=y0(we,w,te),{}.NODE_ENV!=="production"&&w&&Q[0]==="/"&&S0(se,w),D?(D.alias.push(se),{}.NODE_ENV!=="production"&&C0(D,se)):(G=G||se,G!==se&&G.alias.push(se),k&&v.name&&!Uh(se)&&({}.NODE_ENV!=="production"&&x0(v,w),u(v.name))),Bh(se)&&h(se),U.children){const he=U.children;for(let be=0;be<he.length;be++)a(he[be],se,D&&D.children[be])}D=D||se}return G?()=>{u(G)}:Mo}function u(v){if(Ah(v)){const w=i.get(v);w&&(i.delete(v),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(v);w>-1&&(s.splice(w,1),v.record.name&&i.delete(v.record.name),v.children.forEach(u),v.alias.forEach(u))}}function c(){return s}function h(v){const w=O0(v,s);s.splice(w,0,v),v.record.name&&!Uh(v)&&i.set(v.record.name,v)}function m(v,w){let D,k={},U,te;if("name"in v&&v.name){if(D=i.get(v.name),!D)throw Ln(1,{location:v});if({}.NODE_ENV!=="production"){const G=Object.keys(v.params||{}).filter(we=>!D.keys.find(Q=>Q.name===we));G.length&&He(`Discarded invalid param(s) "${G.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}te=D.record.name,k=et(Vh(w.params,D.keys.filter(G=>!G.optional).concat(D.parent?D.parent.keys.filter(G=>G.optional):[]).map(G=>G.name)),v.params&&Vh(v.params,D.keys.map(G=>G.name))),U=D.stringify(k)}else if(v.path!=null)U=v.path,{}.NODE_ENV!=="production"&&!U.startsWith("/")&&He(`The Matcher cannot resolve relative paths but received "${U}". Unless you directly called \`matcher.resolve("${U}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),D=s.find(G=>G.re.test(U)),D&&(k=D.parse(U),te=D.record.name);else{if(D=w.name?i.get(w.name):s.find(G=>G.re.test(w.path)),!D)throw Ln(1,{location:v,currentLocation:w});te=D.record.name,k=et({},w.params,v.params),U=D.stringify(k)}const T=[];let se=D;for(;se;)T.unshift(se.record),se=se.parent;return{name:te,path:U,params:k,matched:T,meta:E0(T)}}e.forEach(v=>a(v));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:m,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:n}}function Vh(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function Fh(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:w0(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function w0(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function Uh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function E0(e){return e.reduce((t,s)=>et(t,s.meta),{})}function Lh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function uu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function C0(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(uu.bind(null,s)))return He(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(uu.bind(null,s)))return He(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function D0(e,t){t&&t.record.name&&!e.name&&!e.path&&He(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function x0(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function S0(e,t){for(const s of t.keys)if(!e.keys.find(uu.bind(null,s)))return He(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function O0(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;kh(e,t[a])<0?i=a:s=a+1}const n=I0(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&He(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function I0(e){let t=e;for(;t=t.parent;)if(Bh(t)&&kh(e,t)===0)return t}function Bh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function N0(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(bh," "),u=a.indexOf("="),c=Un(u<0?a:a.slice(0,u)),h=u<0?null:Un(a.slice(u+1));if(c in t){let m=t[c];ds(m)||(m=t[c]=[m]),m.push(h)}else t[c]=h}return t}function $h(e){let t="";for(let s in e){const i=e[s];if(s=qb(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(ds(i)?i.map(a=>a&&ou(a)):[i&&ou(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function T0(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=ds(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const A0=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),jh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Qi=Symbol({}.NODE_ENV!=="production"?"router":""),cu=Symbol({}.NODE_ENV!=="production"?"route location":""),du=Symbol({}.NODE_ENV!=="production"?"router view location":"");function Ro(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Ur(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,h)=>{const m=w=>{w===!1?h(Ln(4,{from:s,to:t})):w instanceof Error?h(w):Yi(w)?h(Ln(2,{from:t,to:w})):(u&&i.enterCallbacks[n]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?M0(m,t,s):m));let v=Promise.resolve(p);if(e.length<3&&(v=v.then(m)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)v=v.then(D=>m._called?D:(He(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!m._called){He(w),h(new Error("Invalid navigation guard"));return}}v.catch(w=>h(w))})}function M0(e,t,s){let i=0;return function(){i++===1&&He(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function fu(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&He(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw He(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){He(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const m=h;h=()=>m}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,He(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(_h(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Ur(p,s,i,u,c,n))}else{let m=h();({}).NODE_ENV!=="production"&&!("catch"in m)&&(He(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),m=Promise.resolve(m)),a.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const v=Pb(p)?p.default:p;u.mods[c]=p,u.components[c]=v;const D=(v.__vccOpts||v)[t];return D&&Ur(D,s,i,u,c,n)()}))}}}return a}function Hh(e){const t=Rs(Qi),s=Rs(cu);let i=!1,n=null;const a=Fs(()=>{const p=Ir(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==n)&&(Yi(p)||(i?He(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,n,`
- props:`,e):He(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),n=p,i=!0),t.resolve(p)}),u=Fs(()=>{const{matched:p}=a.value,{length:v}=p,w=p[v-1],D=s.matched;if(!w||!D.length)return-1;const k=D.findIndex(Vr.bind(null,w));if(k>-1)return k;const U=qh(p[v-2]);return v>1&&qh(w)===U&&D[D.length-1].path!==U?D.findIndex(Vr.bind(null,p[v-2])):k}),c=Fs(()=>u.value>-1&&V0(s.params,a.value.params)),h=Fs(()=>u.value>-1&&u.value===s.matched.length-1&&Sh(s.params,a.value.params));function m(p={}){if(R0(p)){const v=t[Ir(e.replace)?"replace":"push"](Ir(e.to)).catch(Mo);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>v),v}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Vi();if(p){const v={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(v),Q_(()=>{v.route=a.value,v.isActive=c.value,v.isExactActive=h.value,v.error=Yi(Ir(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Fs(()=>a.value.href),isActive:c,isExactActive:h,navigate:m}}function P0(e){return e.length===1?e[0]:e}const k0=Bd({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Hh,setup(e,{slots:t}){const s=hi(Hh(e)),{options:i}=Rs(Qi),n=Fs(()=>({[zh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[zh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&P0(t.default(s));return e.custom?a:zl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function R0(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function V0(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!ds(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function qh(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const zh=(e,t,s)=>e??t??s,F0=Bd({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&L0();const i=Rs(du),n=Fs(()=>e.route||i.value),a=Rs(jh,0),u=Fs(()=>{let m=Ir(a);const{matched:p}=n.value;let v;for(;(v=p[m])&&!v.components;)m++;return m}),c=Fs(()=>n.value.matched[u.value]);Ai(jh,Fs(()=>u.value+1)),Ai(A0,c),Ai(du,n);const h=cd();return kn(()=>[h.value,c.value,e.name],([m,p,v],[w,D,k])=>{p&&(p.instances[v]=m,D&&D!==p&&m&&m===w&&(p.leaveGuards.size||(p.leaveGuards=D.leaveGuards),p.updateGuards.size||(p.updateGuards=D.updateGuards))),m&&p&&(!D||!Vr(p,D)||!w)&&(p.enterCallbacks[v]||[]).forEach(U=>U(m))},{flush:"post"}),()=>{const m=n.value,p=e.name,v=c.value,w=v&&v.components[p];if(!w)return Wh(s.default,{Component:w,route:m});const D=v.props[p],k=D?D===!0?m.params:typeof D=="function"?D(m):D:null,te=zl(w,et({},k,t,{onVnodeUnmounted:T=>{T.component.isUnmounted&&(v.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&hr&&te.ref){const T={depth:u.value,name:v.name,path:v.path,meta:v.meta};(ds(te.ref)?te.ref.map(G=>G.i):[te.ref.i]).forEach(G=>{G.__vrv_devtools=T})}return Wh(s.default,{Component:te,route:m})||te}}});function Wh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const U0=F0;function L0(){const e=Vi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";He(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Vo(e,t){const s=et({},e,{matched:e.matched.map(i=>Q0(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Zi(e){return{_custom:{display:e}}}let B0=0;function $0(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=B0++;Xl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((p,v)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Vo(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:p,componentInstance:v})=>{if(v.__vrv_devtools){const w=v.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Gh})}ds(v.__vrl_devtools)&&(v.__devtoolsApi=n,v.__vrl_devtools.forEach(w=>{let D=w.route.path,k=Qh,U="",te=0;w.error?(D=w.error,k=W0,te=G0):w.isExactActive?(k=Yh,U="This is exactly active"):w.isActive&&(k=Kh,U="This link is active"),p.tags.push({label:D,textColor:te,tooltip:U,backgroundColor:k})}))}),kn(t.currentRoute,()=>{h(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,v)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:v.fullPath,logType:"error",time:n.now(),data:{error:p},groupId:v.meta.__navigationId}})});let u=0;t.beforeEach((p,v)=>{const w={guard:Zi("beforeEach"),from:Vo(v,"Current Location during this navigation"),to:Vo(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,v,w)=>{const D={guard:Zi("afterEach")};w?(D.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},D.status=Zi("❌")):D.status=Zi("✅"),D.from=Vo(v,"Current Location during this navigation"),D.to=Vo(p,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:n.now(),data:D,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!m)return;const p=m;let v=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);v.forEach(Xh),p.filter&&(v=v.filter(w=>hu(w,p.filter.toLowerCase()))),v.forEach(w=>Jh(w,t.currentRoute.value)),p.rootNodes=v.map(Zh)}let m;n.on.getInspectorTree(p=>{m=p,p.app===e&&p.inspectorId===c&&h()}),n.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(D=>D.record.__vd_id===p.nodeId);w&&(p.state={options:H0(w)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function j0(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function H0(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${j0(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const Gh=15485081,Kh=2450411,Yh=8702998,q0=2282478,Qh=16486972,z0=6710886,W0=16704226,G0=12131356;function Zh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:q0}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Qh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Gh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Yh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Kh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:z0});let i=s.__vd_id;return i==null&&(i=String(K0++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Zh)}}let K0=0;const Y0=/^\/(.*)\/([a-z]*)$/;function Jh(e,t){const s=t.matched.length&&Vr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Vr(i,e.record))),e.children.forEach(i=>Jh(i,t))}function Xh(e){e.__vd_match=!1,e.children.forEach(Xh)}function hu(e,t){const s=String(e.re).match(Y0);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>hu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=Un(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>hu(u,t))}function Q0(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Z0(e){const t=b0(e.routes,e),s=e.parseQuery||N0,i=e.stringifyQuery||$h,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=Ro(),u=Ro(),c=Ro(),h=Ov(Fr);let m=Fr;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=ru.bind(null,R=>""+R),v=ru.bind(null,Wb),w=ru.bind(null,Un);function D(R,oe){let ne,me;return Ah(R)?(ne=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!ne&&He(`Parent route "${String(R)}" not found when adding child route`,oe),me=oe):me=R,t.addRoute(me,ne)}function k(R){const oe=t.getRecordMatcher(R);oe?t.removeRoute(oe):{}.NODE_ENV!=="production"&&He(`Cannot remove non-existent route "${String(R)}"`)}function U(){return t.getRoutes().map(R=>R.record)}function te(R){return!!t.getRecordMatcher(R)}function T(R,oe){if(oe=et({},oe||h.value),typeof R=="string"){const b=iu(s,R,oe.path),C=t.resolve({path:b.path},oe),P=n.createHref(b.fullPath);return{}.NODE_ENV!=="production"&&(P.startsWith("//")?He(`Location "${R}" resolved to "${P}". A resolved location cannot start with multiple slashes.`):C.matched.length||He(`No match found for location with path "${R}"`)),et(b,C,{params:w(C.params),hash:Un(b.hash),redirectedFrom:void 0,href:P})}if({}.NODE_ENV!=="production"&&!Yi(R))return He(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),T({});let ne;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&He(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),ne=et({},R,{path:iu(s,R.path,oe.path).path});else{const b=et({},R.params);for(const C in b)b[C]==null&&delete b[C];ne=et({},R,{params:v(b)}),oe.params=v(oe.params)}const me=t.resolve(ne,oe),Re=R.hash||"";({}).NODE_ENV!=="production"&&Re&&!Re.startsWith("#")&&He(`A \`hash\` should always start with the character "#". Replace "${Re}" with "#${Re}".`),me.params=p(w(me.params));const at=Yb(i,et({},R,{hash:Hb(Re),path:me.path})),Ve=n.createHref(at);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?He(`Location "${R}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):me.matched.length||He(`No match found for location with path "${R.path!=null?R.path:R}"`)),et({fullPath:at,hash:Re,query:i===$h?T0(R.query):R.query||{}},me,{redirectedFrom:void 0,href:Ve})}function se(R){return typeof R=="string"?iu(s,R,h.value.path):et({},R)}function G(R,oe){if(m!==R)return Ln(8,{from:oe,to:R})}function we(R){return be(R)}function Q(R){return we(et(se(R),{replace:!0}))}function he(R){const oe=R.matched[R.matched.length-1];if(oe&&oe.redirect){const{redirect:ne}=oe;let me=typeof ne=="function"?ne(R):ne;if(typeof me=="string"&&(me=me.includes("?")||me.includes("#")?me=se(me):{path:me},me.params={}),{}.NODE_ENV!=="production"&&me.path==null&&!("name"in me))throw He(`Invalid redirect found:
${JSON.stringify(me,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return et({query:R.query,hash:R.hash,params:me.path!=null?{}:R.params},me)}}function be(R,oe){const ne=m=T(R),me=h.value,Re=R.state,at=R.force,Ve=R.replace===!0,b=he(ne);if(b)return be(et(se(b),{state:typeof b=="object"?et({},Re,b.state):Re,force:at,replace:Ve}),oe||ne);const C=ne;C.redirectedFrom=oe;let P;return!at&&xh(i,me,ne)&&(P=Ln(16,{to:C,from:me}),yt(me,me,!0,!1)),(P?Promise.resolve(P):ie(C,me)).catch(F=>pr(F)?pr(F,2)?F:Xt(F):Ee(F,C,me)).then(F=>{if(F){if(pr(F,2))return{}.NODE_ENV!=="production"&&xh(i,T(F.to),C)&&oe&&(oe._count=oe._count?oe._count+1:1)>30?(He(`Detected a possibly infinite redirection in a navigation guard when going from "${me.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):be(et({replace:Ve},se(F.to),{state:typeof F.to=="object"?et({},Re,F.to.state):Re,force:at}),oe||C)}else F=ae(C,me,!0,Ve,Re);return ke(C,me,F),F})}function Ae(R,oe){const ne=G(R,oe);return ne?Promise.reject(ne):Promise.resolve()}function le(R){const oe=fs.values().next().value;return oe&&typeof oe.runWithContext=="function"?oe.runWithContext(R):R()}function ie(R,oe){let ne;const[me,Re,at]=J0(R,oe);ne=fu(me.reverse(),"beforeRouteLeave",R,oe);for(const b of me)b.leaveGuards.forEach(C=>{ne.push(Ur(C,R,oe))});const Ve=Ae.bind(null,R,oe);return ne.push(Ve),hs(ne).then(()=>{ne=[];for(const b of a.list())ne.push(Ur(b,R,oe));return ne.push(Ve),hs(ne)}).then(()=>{ne=fu(Re,"beforeRouteUpdate",R,oe);for(const b of Re)b.updateGuards.forEach(C=>{ne.push(Ur(C,R,oe))});return ne.push(Ve),hs(ne)}).then(()=>{ne=[];for(const b of at)if(b.beforeEnter)if(ds(b.beforeEnter))for(const C of b.beforeEnter)ne.push(Ur(C,R,oe));else ne.push(Ur(b.beforeEnter,R,oe));return ne.push(Ve),hs(ne)}).then(()=>(R.matched.forEach(b=>b.enterCallbacks={}),ne=fu(at,"beforeRouteEnter",R,oe,le),ne.push(Ve),hs(ne))).then(()=>{ne=[];for(const b of u.list())ne.push(Ur(b,R,oe));return ne.push(Ve),hs(ne)}).catch(b=>pr(b,8)?b:Promise.reject(b))}function ke(R,oe,ne){c.list().forEach(me=>le(()=>me(R,oe,ne)))}function ae(R,oe,ne,me,Re){const at=G(R,oe);if(at)return at;const Ve=oe===Fr,b=hr?history.state:{};ne&&(me||Ve?n.replace(R.fullPath,et({scroll:Ve&&b&&b.scroll},Re)):n.push(R.fullPath,Re)),h.value=R,yt(R,oe,ne,Ve),Xt()}let We;function J(){We||(We=n.listen((R,oe,ne)=>{if(!zt.listening)return;const me=T(R),Re=he(me);if(Re){be(et(Re,{replace:!0,force:!0}),me).catch(Mo);return}m=me;const at=h.value;hr&&r0(Ih(at.fullPath,ne.delta),Ki()),ie(me,at).catch(Ve=>pr(Ve,12)?Ve:pr(Ve,2)?(be(et(se(Ve.to),{force:!0}),me).then(b=>{pr(b,20)&&!ne.delta&&ne.type===Po.pop&&n.go(-1,!1)}).catch(Mo),Promise.reject()):(ne.delta&&n.go(-ne.delta,!1),Ee(Ve,me,at))).then(Ve=>{Ve=Ve||ae(me,at,!1),Ve&&(ne.delta&&!pr(Ve,8)?n.go(-ne.delta,!1):ne.type===Po.pop&&pr(Ve,20)&&n.go(-1,!1)),ke(me,at,Ve)}).catch(Mo)}))}let $e=Ro(),ut=Ro(),Oe;function Ee(R,oe,ne){Xt(R);const me=ut.list();return me.length?me.forEach(Re=>Re(R,oe,ne)):({}.NODE_ENV!=="production"&&He("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function Ut(){return Oe&&h.value!==Fr?Promise.resolve():new Promise((R,oe)=>{$e.add([R,oe])})}function Xt(R){return Oe||(Oe=!R,J(),$e.list().forEach(([oe,ne])=>R?ne(R):oe()),$e.reset()),R}function yt(R,oe,ne,me){const{scrollBehavior:Re}=e;if(!hr||!Re)return Promise.resolve();const at=!ne&&n0(Ih(R.fullPath,0))||(me||!ne)&&history.state&&history.state.scroll||null;return vl().then(()=>Re(R,oe,at)).then(Ve=>Ve&&s0(Ve)).catch(Ve=>Ee(Ve,R,oe))}const de=R=>n.go(R);let Ke;const fs=new Set,zt={currentRoute:h,listening:!0,addRoute:D,removeRoute:k,clearRoutes:t.clearRoutes,hasRoute:te,getRoutes:U,resolve:T,options:e,push:we,replace:Q,go:de,back:()=>de(-1),forward:()=>de(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ut.add,isReady:Ut,install(R){const oe=this;R.component("RouterLink",k0),R.component("RouterView",U0),R.config.globalProperties.$router=oe,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Ir(h)}),hr&&!Ke&&h.value===Fr&&(Ke=!0,we(n.location).catch(Re=>{({}).NODE_ENV!=="production"&&He("Unexpected error when starting the router:",Re)}));const ne={};for(const Re in Fr)Object.defineProperty(ne,Re,{get:()=>h.value[Re],enumerable:!0});R.provide(Qi,oe),R.provide(cu,ld(ne)),R.provide(du,h);const me=R.unmount;fs.add(R),R.unmount=function(){fs.delete(R),fs.size<1&&(m=Fr,We&&We(),We=null,h.value=Fr,Ke=!1,Oe=!1),me()},{}.NODE_ENV!=="production"&&hr&&$0(R,oe,t)}};function hs(R){return R.reduce((oe,ne)=>oe.then(()=>le(ne)),Promise.resolve())}return zt}function J0(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(m=>Vr(m,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(m=>Vr(m,h))||n.push(h))}return[s,i,n]}function Ji(){return Rs(Qi)}function ep(e){return Rs(cu)}const tp="data:image/svg+xml;base64,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";var Fo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Xi={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Xi.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",m=500,p="__lodash_placeholder__",v=1,w=2,D=4,k=1,U=2,te=1,T=2,se=4,G=8,we=16,Q=32,he=64,be=128,Ae=256,le=512,ie=30,ke="...",ae=800,We=16,J=1,$e=2,ut=3,Oe=1/0,Ee=9007199254740991,Ut=17976931348623157e292,Xt=0/0,yt=**********,de=yt-1,Ke=yt>>>1,fs=[["ary",be],["bind",te],["bindKey",T],["curry",G],["curryRight",we],["flip",le],["partial",Q],["partialRight",he],["rearg",Ae]],zt="[object Arguments]",hs="[object Array]",R="[object AsyncFunction]",oe="[object Boolean]",ne="[object Date]",me="[object DOMException]",Re="[object Error]",at="[object Function]",Ve="[object GeneratorFunction]",b="[object Map]",C="[object Number]",P="[object Null]",F="[object Object]",j="[object Promise]",q="[object Proxy]",ee="[object RegExp]",K="[object Set]",Z="[object String]",z="[object Symbol]",Ce="[object Undefined]",re="[object WeakMap]",_e="[object WeakSet]",De="[object ArrayBuffer]",Ue="[object DataView]",st="[object Float32Array]",Je="[object Float64Array]",Lt="[object Int8Array]",St="[object Int16Array]",es="[object Int32Array]",Bt="[object Uint8Array]",gr="[object Uint8ClampedArray]",$n="[object Uint16Array]",Tt="[object Uint32Array]",Es=/\b__p \+= '';/g,aa=/\b(__p \+=) '' \+/g,mN=/(__e\(.*?\)|\b__t\)) \+\n'';/g,pp=/&(?:amp|lt|gt|quot|#39);/g,mp=/[&<>"']/g,gN=RegExp(pp.source),vN=RegExp(mp.source),_N=/<%-([\s\S]+?)%>/g,yN=/<%([\s\S]+?)%>/g,gp=/<%=([\s\S]+?)%>/g,bN=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,wN=/^\w*$/,EN=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Cu=/[\\^$.*+?()[\]{}|]/g,CN=RegExp(Cu.source),Du=/^\s+/,DN=/\s/,xN=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,SN=/\{\n\/\* \[wrapped with (.+)\] \*/,ON=/,? & /,IN=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,NN=/[()=,{}\[\]\/\s]/,TN=/\\(\\)?/g,AN=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,vp=/\w*$/,MN=/^[-+]0x[0-9a-f]+$/i,PN=/^0b[01]+$/i,kN=/^\[object .+?Constructor\]$/,RN=/^0o[0-7]+$/i,VN=/^(?:0|[1-9]\d*)$/,FN=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,la=/($^)/,UN=/['\n\r\u2028\u2029\\]/g,ua="\\ud800-\\udfff",LN="\\u0300-\\u036f",BN="\\ufe20-\\ufe2f",$N="\\u20d0-\\u20ff",_p=LN+BN+$N,yp="\\u2700-\\u27bf",bp="a-z\\xdf-\\xf6\\xf8-\\xff",jN="\\xac\\xb1\\xd7\\xf7",HN="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",qN="\\u2000-\\u206f",zN=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",wp="A-Z\\xc0-\\xd6\\xd8-\\xde",Ep="\\ufe0e\\ufe0f",Cp=jN+HN+qN+zN,xu="['’]",WN="["+ua+"]",Dp="["+Cp+"]",ca="["+_p+"]",xp="\\d+",GN="["+yp+"]",Sp="["+bp+"]",Op="[^"+ua+Cp+xp+yp+bp+wp+"]",Su="\\ud83c[\\udffb-\\udfff]",KN="(?:"+ca+"|"+Su+")",Ip="[^"+ua+"]",Ou="(?:\\ud83c[\\udde6-\\uddff]){2}",Iu="[\\ud800-\\udbff][\\udc00-\\udfff]",jn="["+wp+"]",Np="\\u200d",Tp="(?:"+Sp+"|"+Op+")",YN="(?:"+jn+"|"+Op+")",Ap="(?:"+xu+"(?:d|ll|m|re|s|t|ve))?",Mp="(?:"+xu+"(?:D|LL|M|RE|S|T|VE))?",Pp=KN+"?",kp="["+Ep+"]?",QN="(?:"+Np+"(?:"+[Ip,Ou,Iu].join("|")+")"+kp+Pp+")*",ZN="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",JN="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Rp=kp+Pp+QN,XN="(?:"+[GN,Ou,Iu].join("|")+")"+Rp,eT="(?:"+[Ip+ca+"?",ca,Ou,Iu,WN].join("|")+")",tT=RegExp(xu,"g"),sT=RegExp(ca,"g"),Nu=RegExp(Su+"(?="+Su+")|"+eT+Rp,"g"),rT=RegExp([jn+"?"+Sp+"+"+Ap+"(?="+[Dp,jn,"$"].join("|")+")",YN+"+"+Mp+"(?="+[Dp,jn+Tp,"$"].join("|")+")",jn+"?"+Tp+"+"+Ap,jn+"+"+Mp,JN,ZN,xp,XN].join("|"),"g"),nT=RegExp("["+Np+ua+_p+Ep+"]"),oT=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,iT=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],aT=-1,ht={};ht[st]=ht[Je]=ht[Lt]=ht[St]=ht[es]=ht[Bt]=ht[gr]=ht[$n]=ht[Tt]=!0,ht[zt]=ht[hs]=ht[De]=ht[oe]=ht[Ue]=ht[ne]=ht[Re]=ht[at]=ht[b]=ht[C]=ht[F]=ht[ee]=ht[K]=ht[Z]=ht[re]=!1;var dt={};dt[zt]=dt[hs]=dt[De]=dt[Ue]=dt[oe]=dt[ne]=dt[st]=dt[Je]=dt[Lt]=dt[St]=dt[es]=dt[b]=dt[C]=dt[F]=dt[ee]=dt[K]=dt[Z]=dt[z]=dt[Bt]=dt[gr]=dt[$n]=dt[Tt]=!0,dt[Re]=dt[at]=dt[re]=!1;var lT={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},uT={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},cT={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},dT={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},fT=parseFloat,hT=parseInt,Vp=typeof Fo=="object"&&Fo&&Fo.Object===Object&&Fo,pT=typeof self=="object"&&self&&self.Object===Object&&self,$t=Vp||pT||Function("return this")(),Tu=t&&!t.nodeType&&t,gn=Tu&&!0&&e&&!e.nodeType&&e,Fp=gn&&gn.exports===Tu,Au=Fp&&Vp.process,Cs=function(){try{var S=gn&&gn.require&&gn.require("util").types;return S||Au&&Au.binding&&Au.binding("util")}catch{}}(),Up=Cs&&Cs.isArrayBuffer,Lp=Cs&&Cs.isDate,Bp=Cs&&Cs.isMap,$p=Cs&&Cs.isRegExp,jp=Cs&&Cs.isSet,Hp=Cs&&Cs.isTypedArray;function ps(S,V,M){switch(M.length){case 0:return S.call(V);case 1:return S.call(V,M[0]);case 2:return S.call(V,M[0],M[1]);case 3:return S.call(V,M[0],M[1],M[2])}return S.apply(V,M)}function mT(S,V,M,fe){for(var Pe=-1,rt=S==null?0:S.length;++Pe<rt;){var At=S[Pe];V(fe,At,M(At),S)}return fe}function Ds(S,V){for(var M=-1,fe=S==null?0:S.length;++M<fe&&V(S[M],M,S)!==!1;);return S}function gT(S,V){for(var M=S==null?0:S.length;M--&&V(S[M],M,S)!==!1;);return S}function qp(S,V){for(var M=-1,fe=S==null?0:S.length;++M<fe;)if(!V(S[M],M,S))return!1;return!0}function Br(S,V){for(var M=-1,fe=S==null?0:S.length,Pe=0,rt=[];++M<fe;){var At=S[M];V(At,M,S)&&(rt[Pe++]=At)}return rt}function da(S,V){var M=S==null?0:S.length;return!!M&&Hn(S,V,0)>-1}function Mu(S,V,M){for(var fe=-1,Pe=S==null?0:S.length;++fe<Pe;)if(M(V,S[fe]))return!0;return!1}function gt(S,V){for(var M=-1,fe=S==null?0:S.length,Pe=Array(fe);++M<fe;)Pe[M]=V(S[M],M,S);return Pe}function $r(S,V){for(var M=-1,fe=V.length,Pe=S.length;++M<fe;)S[Pe+M]=V[M];return S}function Pu(S,V,M,fe){var Pe=-1,rt=S==null?0:S.length;for(fe&&rt&&(M=S[++Pe]);++Pe<rt;)M=V(M,S[Pe],Pe,S);return M}function vT(S,V,M,fe){var Pe=S==null?0:S.length;for(fe&&Pe&&(M=S[--Pe]);Pe--;)M=V(M,S[Pe],Pe,S);return M}function ku(S,V){for(var M=-1,fe=S==null?0:S.length;++M<fe;)if(V(S[M],M,S))return!0;return!1}var _T=Ru("length");function yT(S){return S.split("")}function bT(S){return S.match(IN)||[]}function zp(S,V,M){var fe;return M(S,function(Pe,rt,At){if(V(Pe,rt,At))return fe=rt,!1}),fe}function fa(S,V,M,fe){for(var Pe=S.length,rt=M+(fe?1:-1);fe?rt--:++rt<Pe;)if(V(S[rt],rt,S))return rt;return-1}function Hn(S,V,M){return V===V?MT(S,V,M):fa(S,Wp,M)}function wT(S,V,M,fe){for(var Pe=M-1,rt=S.length;++Pe<rt;)if(fe(S[Pe],V))return Pe;return-1}function Wp(S){return S!==S}function Gp(S,V){var M=S==null?0:S.length;return M?Fu(S,V)/M:Xt}function Ru(S){return function(V){return V==null?s:V[S]}}function Vu(S){return function(V){return S==null?s:S[V]}}function Kp(S,V,M,fe,Pe){return Pe(S,function(rt,At,ct){M=fe?(fe=!1,rt):V(M,rt,At,ct)}),M}function ET(S,V){var M=S.length;for(S.sort(V);M--;)S[M]=S[M].value;return S}function Fu(S,V){for(var M,fe=-1,Pe=S.length;++fe<Pe;){var rt=V(S[fe]);rt!==s&&(M=M===s?rt:M+rt)}return M}function Uu(S,V){for(var M=-1,fe=Array(S);++M<S;)fe[M]=V(M);return fe}function CT(S,V){return gt(V,function(M){return[M,S[M]]})}function Yp(S){return S&&S.slice(0,Xp(S)+1).replace(Du,"")}function ms(S){return function(V){return S(V)}}function Lu(S,V){return gt(V,function(M){return S[M]})}function qo(S,V){return S.has(V)}function Qp(S,V){for(var M=-1,fe=S.length;++M<fe&&Hn(V,S[M],0)>-1;);return M}function Zp(S,V){for(var M=S.length;M--&&Hn(V,S[M],0)>-1;);return M}function DT(S,V){for(var M=S.length,fe=0;M--;)S[M]===V&&++fe;return fe}var xT=Vu(lT),ST=Vu(uT);function OT(S){return"\\"+dT[S]}function IT(S,V){return S==null?s:S[V]}function qn(S){return nT.test(S)}function NT(S){return oT.test(S)}function TT(S){for(var V,M=[];!(V=S.next()).done;)M.push(V.value);return M}function Bu(S){var V=-1,M=Array(S.size);return S.forEach(function(fe,Pe){M[++V]=[Pe,fe]}),M}function Jp(S,V){return function(M){return S(V(M))}}function jr(S,V){for(var M=-1,fe=S.length,Pe=0,rt=[];++M<fe;){var At=S[M];(At===V||At===p)&&(S[M]=p,rt[Pe++]=M)}return rt}function ha(S){var V=-1,M=Array(S.size);return S.forEach(function(fe){M[++V]=fe}),M}function AT(S){var V=-1,M=Array(S.size);return S.forEach(function(fe){M[++V]=[fe,fe]}),M}function MT(S,V,M){for(var fe=M-1,Pe=S.length;++fe<Pe;)if(S[fe]===V)return fe;return-1}function PT(S,V,M){for(var fe=M+1;fe--;)if(S[fe]===V)return fe;return fe}function zn(S){return qn(S)?RT(S):_T(S)}function Ls(S){return qn(S)?VT(S):yT(S)}function Xp(S){for(var V=S.length;V--&&DN.test(S.charAt(V)););return V}var kT=Vu(cT);function RT(S){for(var V=Nu.lastIndex=0;Nu.test(S);)++V;return V}function VT(S){return S.match(Nu)||[]}function FT(S){return S.match(rT)||[]}var UT=function S(V){V=V==null?$t:Wn.defaults($t.Object(),V,Wn.pick($t,iT));var M=V.Array,fe=V.Date,Pe=V.Error,rt=V.Function,At=V.Math,ct=V.Object,$u=V.RegExp,LT=V.String,xs=V.TypeError,pa=M.prototype,BT=rt.prototype,Gn=ct.prototype,ma=V["__core-js_shared__"],ga=BT.toString,lt=Gn.hasOwnProperty,$T=0,em=function(){var r=/[^.]+$/.exec(ma&&ma.keys&&ma.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),va=Gn.toString,jT=ga.call(ct),HT=$t._,qT=$u("^"+ga.call(lt).replace(Cu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),_a=Fp?V.Buffer:s,Hr=V.Symbol,ya=V.Uint8Array,tm=_a?_a.allocUnsafe:s,ba=Jp(ct.getPrototypeOf,ct),sm=ct.create,rm=Gn.propertyIsEnumerable,wa=pa.splice,nm=Hr?Hr.isConcatSpreadable:s,zo=Hr?Hr.iterator:s,vn=Hr?Hr.toStringTag:s,Ea=function(){try{var r=En(ct,"defineProperty");return r({},"",{}),r}catch{}}(),zT=V.clearTimeout!==$t.clearTimeout&&V.clearTimeout,WT=fe&&fe.now!==$t.Date.now&&fe.now,GT=V.setTimeout!==$t.setTimeout&&V.setTimeout,Ca=At.ceil,Da=At.floor,ju=ct.getOwnPropertySymbols,KT=_a?_a.isBuffer:s,om=V.isFinite,YT=pa.join,QT=Jp(ct.keys,ct),Mt=At.max,Wt=At.min,ZT=fe.now,JT=V.parseInt,im=At.random,XT=pa.reverse,Hu=En(V,"DataView"),Wo=En(V,"Map"),qu=En(V,"Promise"),Kn=En(V,"Set"),Go=En(V,"WeakMap"),Ko=En(ct,"create"),xa=Go&&new Go,Yn={},eA=Cn(Hu),tA=Cn(Wo),sA=Cn(qu),rA=Cn(Kn),nA=Cn(Go),Sa=Hr?Hr.prototype:s,Yo=Sa?Sa.valueOf:s,am=Sa?Sa.toString:s;function _(r){if(bt(r)&&!Fe(r)&&!(r instanceof Ye)){if(r instanceof Ss)return r;if(lt.call(r,"__wrapped__"))return lg(r)}return new Ss(r)}var Qn=function(){function r(){}return function(o){if(!_t(o))return{};if(sm)return sm(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function Oa(){}function Ss(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}_.templateSettings={escape:_N,evaluate:yN,interpolate:gp,variable:"",imports:{_}},_.prototype=Oa.prototype,_.prototype.constructor=_,Ss.prototype=Qn(Oa.prototype),Ss.prototype.constructor=Ss;function Ye(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=yt,this.__views__=[]}function oA(){var r=new Ye(this.__wrapped__);return r.__actions__=ns(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=ns(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=ns(this.__views__),r}function iA(){if(this.__filtered__){var r=new Ye(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function aA(){var r=this.__wrapped__.value(),o=this.__dir__,l=Fe(r),d=o<0,g=l?r.length:0,y=yM(0,g,this.__views__),E=y.start,x=y.end,I=x-E,L=d?x:E-1,B=this.__iteratees__,H=B.length,ue=0,ve=Wt(I,this.__takeCount__);if(!l||!d&&g==I&&ve==I)return Am(r,this.__actions__);var Ie=[];e:for(;I--&&ue<ve;){L+=o;for(var je=-1,Ne=r[L];++je<H;){var Ge=B[je],Qe=Ge.iteratee,_s=Ge.type,rs=Qe(Ne);if(_s==$e)Ne=rs;else if(!rs){if(_s==J)continue e;break e}}Ie[ue++]=Ne}return Ie}Ye.prototype=Qn(Oa.prototype),Ye.prototype.constructor=Ye;function _n(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function lA(){this.__data__=Ko?Ko(null):{},this.size=0}function uA(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function cA(r){var o=this.__data__;if(Ko){var l=o[r];return l===h?s:l}return lt.call(o,r)?o[r]:s}function dA(r){var o=this.__data__;return Ko?o[r]!==s:lt.call(o,r)}function fA(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Ko&&o===s?h:o,this}_n.prototype.clear=lA,_n.prototype.delete=uA,_n.prototype.get=cA,_n.prototype.has=dA,_n.prototype.set=fA;function vr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function hA(){this.__data__=[],this.size=0}function pA(r){var o=this.__data__,l=Ia(o,r);if(l<0)return!1;var d=o.length-1;return l==d?o.pop():wa.call(o,l,1),--this.size,!0}function mA(r){var o=this.__data__,l=Ia(o,r);return l<0?s:o[l][1]}function gA(r){return Ia(this.__data__,r)>-1}function vA(r,o){var l=this.__data__,d=Ia(l,r);return d<0?(++this.size,l.push([r,o])):l[d][1]=o,this}vr.prototype.clear=hA,vr.prototype.delete=pA,vr.prototype.get=mA,vr.prototype.has=gA,vr.prototype.set=vA;function _r(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function _A(){this.size=0,this.__data__={hash:new _n,map:new(Wo||vr),string:new _n}}function yA(r){var o=Ba(this,r).delete(r);return this.size-=o?1:0,o}function bA(r){return Ba(this,r).get(r)}function wA(r){return Ba(this,r).has(r)}function EA(r,o){var l=Ba(this,r),d=l.size;return l.set(r,o),this.size+=l.size==d?0:1,this}_r.prototype.clear=_A,_r.prototype.delete=yA,_r.prototype.get=bA,_r.prototype.has=wA,_r.prototype.set=EA;function yn(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new _r;++o<l;)this.add(r[o])}function CA(r){return this.__data__.set(r,h),this}function DA(r){return this.__data__.has(r)}yn.prototype.add=yn.prototype.push=CA,yn.prototype.has=DA;function Bs(r){var o=this.__data__=new vr(r);this.size=o.size}function xA(){this.__data__=new vr,this.size=0}function SA(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function OA(r){return this.__data__.get(r)}function IA(r){return this.__data__.has(r)}function NA(r,o){var l=this.__data__;if(l instanceof vr){var d=l.__data__;if(!Wo||d.length<n-1)return d.push([r,o]),this.size=++l.size,this;l=this.__data__=new _r(d)}return l.set(r,o),this.size=l.size,this}Bs.prototype.clear=xA,Bs.prototype.delete=SA,Bs.prototype.get=OA,Bs.prototype.has=IA,Bs.prototype.set=NA;function lm(r,o){var l=Fe(r),d=!l&&Dn(r),g=!l&&!d&&Kr(r),y=!l&&!d&&!g&&eo(r),E=l||d||g||y,x=E?Uu(r.length,LT):[],I=x.length;for(var L in r)(o||lt.call(r,L))&&!(E&&(L=="length"||g&&(L=="offset"||L=="parent")||y&&(L=="buffer"||L=="byteLength"||L=="byteOffset")||Er(L,I)))&&x.push(L);return x}function um(r){var o=r.length;return o?r[tc(0,o-1)]:s}function TA(r,o){return $a(ns(r),bn(o,0,r.length))}function AA(r){return $a(ns(r))}function zu(r,o,l){(l!==s&&!$s(r[o],l)||l===s&&!(o in r))&&yr(r,o,l)}function Qo(r,o,l){var d=r[o];(!(lt.call(r,o)&&$s(d,l))||l===s&&!(o in r))&&yr(r,o,l)}function Ia(r,o){for(var l=r.length;l--;)if($s(r[l][0],o))return l;return-1}function MA(r,o,l,d){return qr(r,function(g,y,E){o(d,g,l(g),E)}),d}function cm(r,o){return r&&Js(o,Rt(o),r)}function PA(r,o){return r&&Js(o,is(o),r)}function yr(r,o,l){o=="__proto__"&&Ea?Ea(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function Wu(r,o){for(var l=-1,d=o.length,g=M(d),y=r==null;++l<d;)g[l]=y?s:Sc(r,o[l]);return g}function bn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Os(r,o,l,d,g,y){var E,x=o&v,I=o&w,L=o&D;if(l&&(E=g?l(r,d,g,y):l(r)),E!==s)return E;if(!_t(r))return r;var B=Fe(r);if(B){if(E=wM(r),!x)return ns(r,E)}else{var H=Gt(r),ue=H==at||H==Ve;if(Kr(r))return km(r,x);if(H==F||H==zt||ue&&!g){if(E=I||ue?{}:Xm(r),!x)return I?cM(r,PA(E,r)):uM(r,cm(E,r))}else{if(!dt[H])return g?r:{};E=EM(r,H,x)}}y||(y=new Bs);var ve=y.get(r);if(ve)return ve;y.set(r,E),Ig(r)?r.forEach(function(Ne){E.add(Os(Ne,o,l,Ne,r,y))}):Sg(r)&&r.forEach(function(Ne,Ge){E.set(Ge,Os(Ne,o,l,Ge,r,y))});var Ie=L?I?fc:dc:I?is:Rt,je=B?s:Ie(r);return Ds(je||r,function(Ne,Ge){je&&(Ge=Ne,Ne=r[Ge]),Qo(E,Ge,Os(Ne,o,l,Ge,r,y))}),E}function kA(r){var o=Rt(r);return function(l){return dm(l,r,o)}}function dm(r,o,l){var d=l.length;if(r==null)return!d;for(r=ct(r);d--;){var g=l[d],y=o[g],E=r[g];if(E===s&&!(g in r)||!y(E))return!1}return!0}function fm(r,o,l){if(typeof r!="function")throw new xs(u);return ri(function(){r.apply(s,l)},o)}function Zo(r,o,l,d){var g=-1,y=da,E=!0,x=r.length,I=[],L=o.length;if(!x)return I;l&&(o=gt(o,ms(l))),d?(y=Mu,E=!1):o.length>=n&&(y=qo,E=!1,o=new yn(o));e:for(;++g<x;){var B=r[g],H=l==null?B:l(B);if(B=d||B!==0?B:0,E&&H===H){for(var ue=L;ue--;)if(o[ue]===H)continue e;I.push(B)}else y(o,H,d)||I.push(B)}return I}var qr=Lm(Zs),hm=Lm(Ku,!0);function RA(r,o){var l=!0;return qr(r,function(d,g,y){return l=!!o(d,g,y),l}),l}function Na(r,o,l){for(var d=-1,g=r.length;++d<g;){var y=r[d],E=o(y);if(E!=null&&(x===s?E===E&&!vs(E):l(E,x)))var x=E,I=y}return I}function VA(r,o,l,d){var g=r.length;for(l=Le(l),l<0&&(l=-l>g?0:g+l),d=d===s||d>g?g:Le(d),d<0&&(d+=g),d=l>d?0:Tg(d);l<d;)r[l++]=o;return r}function pm(r,o){var l=[];return qr(r,function(d,g,y){o(d,g,y)&&l.push(d)}),l}function jt(r,o,l,d,g){var y=-1,E=r.length;for(l||(l=DM),g||(g=[]);++y<E;){var x=r[y];o>0&&l(x)?o>1?jt(x,o-1,l,d,g):$r(g,x):d||(g[g.length]=x)}return g}var Gu=Bm(),mm=Bm(!0);function Zs(r,o){return r&&Gu(r,o,Rt)}function Ku(r,o){return r&&mm(r,o,Rt)}function Ta(r,o){return Br(o,function(l){return Cr(r[l])})}function wn(r,o){o=Wr(o,r);for(var l=0,d=o.length;r!=null&&l<d;)r=r[Xs(o[l++])];return l&&l==d?r:s}function gm(r,o,l){var d=o(r);return Fe(r)?d:$r(d,l(r))}function ts(r){return r==null?r===s?Ce:P:vn&&vn in ct(r)?_M(r):AM(r)}function Yu(r,o){return r>o}function FA(r,o){return r!=null&&lt.call(r,o)}function UA(r,o){return r!=null&&o in ct(r)}function LA(r,o,l){return r>=Wt(o,l)&&r<Mt(o,l)}function Qu(r,o,l){for(var d=l?Mu:da,g=r[0].length,y=r.length,E=y,x=M(y),I=1/0,L=[];E--;){var B=r[E];E&&o&&(B=gt(B,ms(o))),I=Wt(B.length,I),x[E]=!l&&(o||g>=120&&B.length>=120)?new yn(E&&B):s}B=r[0];var H=-1,ue=x[0];e:for(;++H<g&&L.length<I;){var ve=B[H],Ie=o?o(ve):ve;if(ve=l||ve!==0?ve:0,!(ue?qo(ue,Ie):d(L,Ie,l))){for(E=y;--E;){var je=x[E];if(!(je?qo(je,Ie):d(r[E],Ie,l)))continue e}ue&&ue.push(Ie),L.push(ve)}}return L}function BA(r,o,l,d){return Zs(r,function(g,y,E){o(d,l(g),y,E)}),d}function Jo(r,o,l){o=Wr(o,r),r=rg(r,o);var d=r==null?r:r[Xs(Ns(o))];return d==null?s:ps(d,r,l)}function vm(r){return bt(r)&&ts(r)==zt}function $A(r){return bt(r)&&ts(r)==De}function jA(r){return bt(r)&&ts(r)==ne}function Xo(r,o,l,d,g){return r===o?!0:r==null||o==null||!bt(r)&&!bt(o)?r!==r&&o!==o:HA(r,o,l,d,Xo,g)}function HA(r,o,l,d,g,y){var E=Fe(r),x=Fe(o),I=E?hs:Gt(r),L=x?hs:Gt(o);I=I==zt?F:I,L=L==zt?F:L;var B=I==F,H=L==F,ue=I==L;if(ue&&Kr(r)){if(!Kr(o))return!1;E=!0,B=!1}if(ue&&!B)return y||(y=new Bs),E||eo(r)?Qm(r,o,l,d,g,y):gM(r,o,I,l,d,g,y);if(!(l&k)){var ve=B&&lt.call(r,"__wrapped__"),Ie=H&&lt.call(o,"__wrapped__");if(ve||Ie){var je=ve?r.value():r,Ne=Ie?o.value():o;return y||(y=new Bs),g(je,Ne,l,d,y)}}return ue?(y||(y=new Bs),vM(r,o,l,d,g,y)):!1}function qA(r){return bt(r)&&Gt(r)==b}function Zu(r,o,l,d){var g=l.length,y=g,E=!d;if(r==null)return!y;for(r=ct(r);g--;){var x=l[g];if(E&&x[2]?x[1]!==r[x[0]]:!(x[0]in r))return!1}for(;++g<y;){x=l[g];var I=x[0],L=r[I],B=x[1];if(E&&x[2]){if(L===s&&!(I in r))return!1}else{var H=new Bs;if(d)var ue=d(L,B,I,r,o,H);if(!(ue===s?Xo(B,L,k|U,d,H):ue))return!1}}return!0}function _m(r){if(!_t(r)||SM(r))return!1;var o=Cr(r)?qT:kN;return o.test(Cn(r))}function zA(r){return bt(r)&&ts(r)==ee}function WA(r){return bt(r)&&Gt(r)==K}function GA(r){return bt(r)&&Ga(r.length)&&!!ht[ts(r)]}function ym(r){return typeof r=="function"?r:r==null?as:typeof r=="object"?Fe(r)?Em(r[0],r[1]):wm(r):$g(r)}function Ju(r){if(!si(r))return QT(r);var o=[];for(var l in ct(r))lt.call(r,l)&&l!="constructor"&&o.push(l);return o}function KA(r){if(!_t(r))return TM(r);var o=si(r),l=[];for(var d in r)d=="constructor"&&(o||!lt.call(r,d))||l.push(d);return l}function Xu(r,o){return r<o}function bm(r,o){var l=-1,d=os(r)?M(r.length):[];return qr(r,function(g,y,E){d[++l]=o(g,y,E)}),d}function wm(r){var o=pc(r);return o.length==1&&o[0][2]?tg(o[0][0],o[0][1]):function(l){return l===r||Zu(l,r,o)}}function Em(r,o){return gc(r)&&eg(o)?tg(Xs(r),o):function(l){var d=Sc(l,r);return d===s&&d===o?Oc(l,r):Xo(o,d,k|U)}}function Aa(r,o,l,d,g){r!==o&&Gu(o,function(y,E){if(g||(g=new Bs),_t(y))YA(r,o,E,l,Aa,d,g);else{var x=d?d(_c(r,E),y,E+"",r,o,g):s;x===s&&(x=y),zu(r,E,x)}},is)}function YA(r,o,l,d,g,y,E){var x=_c(r,l),I=_c(o,l),L=E.get(I);if(L){zu(r,l,L);return}var B=y?y(x,I,l+"",r,o,E):s,H=B===s;if(H){var ue=Fe(I),ve=!ue&&Kr(I),Ie=!ue&&!ve&&eo(I);B=I,ue||ve||Ie?Fe(x)?B=x:Et(x)?B=ns(x):ve?(H=!1,B=km(I,!0)):Ie?(H=!1,B=Rm(I,!0)):B=[]:ni(I)||Dn(I)?(B=x,Dn(x)?B=Ag(x):(!_t(x)||Cr(x))&&(B=Xm(I))):H=!1}H&&(E.set(I,B),g(B,I,d,y,E),E.delete(I)),zu(r,l,B)}function Cm(r,o){var l=r.length;if(l)return o+=o<0?l:0,Er(o,l)?r[o]:s}function Dm(r,o,l){o.length?o=gt(o,function(y){return Fe(y)?function(E){return wn(E,y.length===1?y[0]:y)}:y}):o=[as];var d=-1;o=gt(o,ms(xe()));var g=bm(r,function(y,E,x){var I=gt(o,function(L){return L(y)});return{criteria:I,index:++d,value:y}});return ET(g,function(y,E){return lM(y,E,l)})}function QA(r,o){return xm(r,o,function(l,d){return Oc(r,d)})}function xm(r,o,l){for(var d=-1,g=o.length,y={};++d<g;){var E=o[d],x=wn(r,E);l(x,E)&&ei(y,Wr(E,r),x)}return y}function ZA(r){return function(o){return wn(o,r)}}function ec(r,o,l,d){var g=d?wT:Hn,y=-1,E=o.length,x=r;for(r===o&&(o=ns(o)),l&&(x=gt(r,ms(l)));++y<E;)for(var I=0,L=o[y],B=l?l(L):L;(I=g(x,B,I,d))>-1;)x!==r&&wa.call(x,I,1),wa.call(r,I,1);return r}function Sm(r,o){for(var l=r?o.length:0,d=l-1;l--;){var g=o[l];if(l==d||g!==y){var y=g;Er(g)?wa.call(r,g,1):nc(r,g)}}return r}function tc(r,o){return r+Da(im()*(o-r+1))}function JA(r,o,l,d){for(var g=-1,y=Mt(Ca((o-r)/(l||1)),0),E=M(y);y--;)E[d?y:++g]=r,r+=l;return E}function sc(r,o){var l="";if(!r||o<1||o>Ee)return l;do o%2&&(l+=r),o=Da(o/2),o&&(r+=r);while(o);return l}function qe(r,o){return yc(sg(r,o,as),r+"")}function XA(r){return um(to(r))}function eM(r,o){var l=to(r);return $a(l,bn(o,0,l.length))}function ei(r,o,l,d){if(!_t(r))return r;o=Wr(o,r);for(var g=-1,y=o.length,E=y-1,x=r;x!=null&&++g<y;){var I=Xs(o[g]),L=l;if(I==="__proto__"||I==="constructor"||I==="prototype")return r;if(g!=E){var B=x[I];L=d?d(B,I,x):s,L===s&&(L=_t(B)?B:Er(o[g+1])?[]:{})}Qo(x,I,L),x=x[I]}return r}var Om=xa?function(r,o){return xa.set(r,o),r}:as,tM=Ea?function(r,o){return Ea(r,"toString",{configurable:!0,enumerable:!1,value:Nc(o),writable:!0})}:as;function sM(r){return $a(to(r))}function Is(r,o,l){var d=-1,g=r.length;o<0&&(o=-o>g?0:g+o),l=l>g?g:l,l<0&&(l+=g),g=o>l?0:l-o>>>0,o>>>=0;for(var y=M(g);++d<g;)y[d]=r[d+o];return y}function rM(r,o){var l;return qr(r,function(d,g,y){return l=o(d,g,y),!l}),!!l}function Ma(r,o,l){var d=0,g=r==null?d:r.length;if(typeof o=="number"&&o===o&&g<=Ke){for(;d<g;){var y=d+g>>>1,E=r[y];E!==null&&!vs(E)&&(l?E<=o:E<o)?d=y+1:g=y}return g}return rc(r,o,as,l)}function rc(r,o,l,d){var g=0,y=r==null?0:r.length;if(y===0)return 0;o=l(o);for(var E=o!==o,x=o===null,I=vs(o),L=o===s;g<y;){var B=Da((g+y)/2),H=l(r[B]),ue=H!==s,ve=H===null,Ie=H===H,je=vs(H);if(E)var Ne=d||Ie;else L?Ne=Ie&&(d||ue):x?Ne=Ie&&ue&&(d||!ve):I?Ne=Ie&&ue&&!ve&&(d||!je):ve||je?Ne=!1:Ne=d?H<=o:H<o;Ne?g=B+1:y=B}return Wt(y,de)}function Im(r,o){for(var l=-1,d=r.length,g=0,y=[];++l<d;){var E=r[l],x=o?o(E):E;if(!l||!$s(x,I)){var I=x;y[g++]=E===0?0:E}}return y}function Nm(r){return typeof r=="number"?r:vs(r)?Xt:+r}function gs(r){if(typeof r=="string")return r;if(Fe(r))return gt(r,gs)+"";if(vs(r))return am?am.call(r):"";var o=r+"";return o=="0"&&1/r==-Oe?"-0":o}function zr(r,o,l){var d=-1,g=da,y=r.length,E=!0,x=[],I=x;if(l)E=!1,g=Mu;else if(y>=n){var L=o?null:pM(r);if(L)return ha(L);E=!1,g=qo,I=new yn}else I=o?[]:x;e:for(;++d<y;){var B=r[d],H=o?o(B):B;if(B=l||B!==0?B:0,E&&H===H){for(var ue=I.length;ue--;)if(I[ue]===H)continue e;o&&I.push(H),x.push(B)}else g(I,H,l)||(I!==x&&I.push(H),x.push(B))}return x}function nc(r,o){return o=Wr(o,r),r=rg(r,o),r==null||delete r[Xs(Ns(o))]}function Tm(r,o,l,d){return ei(r,o,l(wn(r,o)),d)}function Pa(r,o,l,d){for(var g=r.length,y=d?g:-1;(d?y--:++y<g)&&o(r[y],y,r););return l?Is(r,d?0:y,d?y+1:g):Is(r,d?y+1:0,d?g:y)}function Am(r,o){var l=r;return l instanceof Ye&&(l=l.value()),Pu(o,function(d,g){return g.func.apply(g.thisArg,$r([d],g.args))},l)}function oc(r,o,l){var d=r.length;if(d<2)return d?zr(r[0]):[];for(var g=-1,y=M(d);++g<d;)for(var E=r[g],x=-1;++x<d;)x!=g&&(y[g]=Zo(y[g]||E,r[x],o,l));return zr(jt(y,1),o,l)}function Mm(r,o,l){for(var d=-1,g=r.length,y=o.length,E={};++d<g;){var x=d<y?o[d]:s;l(E,r[d],x)}return E}function ic(r){return Et(r)?r:[]}function ac(r){return typeof r=="function"?r:as}function Wr(r,o){return Fe(r)?r:gc(r,o)?[r]:ag(it(r))}var nM=qe;function Gr(r,o,l){var d=r.length;return l=l===s?d:l,!o&&l>=d?r:Is(r,o,l)}var Pm=zT||function(r){return $t.clearTimeout(r)};function km(r,o){if(o)return r.slice();var l=r.length,d=tm?tm(l):new r.constructor(l);return r.copy(d),d}function lc(r){var o=new r.constructor(r.byteLength);return new ya(o).set(new ya(r)),o}function oM(r,o){var l=o?lc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function iM(r){var o=new r.constructor(r.source,vp.exec(r));return o.lastIndex=r.lastIndex,o}function aM(r){return Yo?ct(Yo.call(r)):{}}function Rm(r,o){var l=o?lc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Vm(r,o){if(r!==o){var l=r!==s,d=r===null,g=r===r,y=vs(r),E=o!==s,x=o===null,I=o===o,L=vs(o);if(!x&&!L&&!y&&r>o||y&&E&&I&&!x&&!L||d&&E&&I||!l&&I||!g)return 1;if(!d&&!y&&!L&&r<o||L&&l&&g&&!d&&!y||x&&l&&g||!E&&g||!I)return-1}return 0}function lM(r,o,l){for(var d=-1,g=r.criteria,y=o.criteria,E=g.length,x=l.length;++d<E;){var I=Vm(g[d],y[d]);if(I){if(d>=x)return I;var L=l[d];return I*(L=="desc"?-1:1)}}return r.index-o.index}function Fm(r,o,l,d){for(var g=-1,y=r.length,E=l.length,x=-1,I=o.length,L=Mt(y-E,0),B=M(I+L),H=!d;++x<I;)B[x]=o[x];for(;++g<E;)(H||g<y)&&(B[l[g]]=r[g]);for(;L--;)B[x++]=r[g++];return B}function Um(r,o,l,d){for(var g=-1,y=r.length,E=-1,x=l.length,I=-1,L=o.length,B=Mt(y-x,0),H=M(B+L),ue=!d;++g<B;)H[g]=r[g];for(var ve=g;++I<L;)H[ve+I]=o[I];for(;++E<x;)(ue||g<y)&&(H[ve+l[E]]=r[g++]);return H}function ns(r,o){var l=-1,d=r.length;for(o||(o=M(d));++l<d;)o[l]=r[l];return o}function Js(r,o,l,d){var g=!l;l||(l={});for(var y=-1,E=o.length;++y<E;){var x=o[y],I=d?d(l[x],r[x],x,l,r):s;I===s&&(I=r[x]),g?yr(l,x,I):Qo(l,x,I)}return l}function uM(r,o){return Js(r,mc(r),o)}function cM(r,o){return Js(r,Zm(r),o)}function ka(r,o){return function(l,d){var g=Fe(l)?mT:MA,y=o?o():{};return g(l,r,xe(d,2),y)}}function Zn(r){return qe(function(o,l){var d=-1,g=l.length,y=g>1?l[g-1]:s,E=g>2?l[2]:s;for(y=r.length>3&&typeof y=="function"?(g--,y):s,E&&ss(l[0],l[1],E)&&(y=g<3?s:y,g=1),o=ct(o);++d<g;){var x=l[d];x&&r(o,x,d,y)}return o})}function Lm(r,o){return function(l,d){if(l==null)return l;if(!os(l))return r(l,d);for(var g=l.length,y=o?g:-1,E=ct(l);(o?y--:++y<g)&&d(E[y],y,E)!==!1;);return l}}function Bm(r){return function(o,l,d){for(var g=-1,y=ct(o),E=d(o),x=E.length;x--;){var I=E[r?x:++g];if(l(y[I],I,y)===!1)break}return o}}function dM(r,o,l){var d=o&te,g=ti(r);function y(){var E=this&&this!==$t&&this instanceof y?g:r;return E.apply(d?l:this,arguments)}return y}function $m(r){return function(o){o=it(o);var l=qn(o)?Ls(o):s,d=l?l[0]:o.charAt(0),g=l?Gr(l,1).join(""):o.slice(1);return d[r]()+g}}function Jn(r){return function(o){return Pu(Lg(Ug(o).replace(tT,"")),r,"")}}function ti(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Qn(r.prototype),d=r.apply(l,o);return _t(d)?d:l}}function fM(r,o,l){var d=ti(r);function g(){for(var y=arguments.length,E=M(y),x=y,I=Xn(g);x--;)E[x]=arguments[x];var L=y<3&&E[0]!==I&&E[y-1]!==I?[]:jr(E,I);if(y-=L.length,y<l)return Wm(r,o,Ra,g.placeholder,s,E,L,s,s,l-y);var B=this&&this!==$t&&this instanceof g?d:r;return ps(B,this,E)}return g}function jm(r){return function(o,l,d){var g=ct(o);if(!os(o)){var y=xe(l,3);o=Rt(o),l=function(x){return y(g[x],x,g)}}var E=r(o,l,d);return E>-1?g[y?o[E]:E]:s}}function Hm(r){return wr(function(o){var l=o.length,d=l,g=Ss.prototype.thru;for(r&&o.reverse();d--;){var y=o[d];if(typeof y!="function")throw new xs(u);if(g&&!E&&La(y)=="wrapper")var E=new Ss([],!0)}for(d=E?d:l;++d<l;){y=o[d];var x=La(y),I=x=="wrapper"?hc(y):s;I&&vc(I[0])&&I[1]==(be|G|Q|Ae)&&!I[4].length&&I[9]==1?E=E[La(I[0])].apply(E,I[3]):E=y.length==1&&vc(y)?E[x]():E.thru(y)}return function(){var L=arguments,B=L[0];if(E&&L.length==1&&Fe(B))return E.plant(B).value();for(var H=0,ue=l?o[H].apply(this,L):B;++H<l;)ue=o[H].call(this,ue);return ue}})}function Ra(r,o,l,d,g,y,E,x,I,L){var B=o&be,H=o&te,ue=o&T,ve=o&(G|we),Ie=o&le,je=ue?s:ti(r);function Ne(){for(var Ge=arguments.length,Qe=M(Ge),_s=Ge;_s--;)Qe[_s]=arguments[_s];if(ve)var rs=Xn(Ne),ys=DT(Qe,rs);if(d&&(Qe=Fm(Qe,d,g,ve)),y&&(Qe=Um(Qe,y,E,ve)),Ge-=ys,ve&&Ge<L){var Ct=jr(Qe,rs);return Wm(r,o,Ra,Ne.placeholder,l,Qe,Ct,x,I,L-Ge)}var js=H?l:this,xr=ue?js[r]:r;return Ge=Qe.length,x?Qe=MM(Qe,x):Ie&&Ge>1&&Qe.reverse(),B&&I<Ge&&(Qe.length=I),this&&this!==$t&&this instanceof Ne&&(xr=je||ti(xr)),xr.apply(js,Qe)}return Ne}function qm(r,o){return function(l,d){return BA(l,r,o(d),{})}}function Va(r,o){return function(l,d){var g;if(l===s&&d===s)return o;if(l!==s&&(g=l),d!==s){if(g===s)return d;typeof l=="string"||typeof d=="string"?(l=gs(l),d=gs(d)):(l=Nm(l),d=Nm(d)),g=r(l,d)}return g}}function uc(r){return wr(function(o){return o=gt(o,ms(xe())),qe(function(l){var d=this;return r(o,function(g){return ps(g,d,l)})})})}function Fa(r,o){o=o===s?" ":gs(o);var l=o.length;if(l<2)return l?sc(o,r):o;var d=sc(o,Ca(r/zn(o)));return qn(o)?Gr(Ls(d),0,r).join(""):d.slice(0,r)}function hM(r,o,l,d){var g=o&te,y=ti(r);function E(){for(var x=-1,I=arguments.length,L=-1,B=d.length,H=M(B+I),ue=this&&this!==$t&&this instanceof E?y:r;++L<B;)H[L]=d[L];for(;I--;)H[L++]=arguments[++x];return ps(ue,g?l:this,H)}return E}function zm(r){return function(o,l,d){return d&&typeof d!="number"&&ss(o,l,d)&&(l=d=s),o=Dr(o),l===s?(l=o,o=0):l=Dr(l),d=d===s?o<l?1:-1:Dr(d),JA(o,l,d,r)}}function Ua(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Ts(o),l=Ts(l)),r(o,l)}}function Wm(r,o,l,d,g,y,E,x,I,L){var B=o&G,H=B?E:s,ue=B?s:E,ve=B?y:s,Ie=B?s:y;o|=B?Q:he,o&=~(B?he:Q),o&se||(o&=~(te|T));var je=[r,o,g,ve,H,Ie,ue,x,I,L],Ne=l.apply(s,je);return vc(r)&&ng(Ne,je),Ne.placeholder=d,og(Ne,r,o)}function cc(r){var o=At[r];return function(l,d){if(l=Ts(l),d=d==null?0:Wt(Le(d),292),d&&om(l)){var g=(it(l)+"e").split("e"),y=o(g[0]+"e"+(+g[1]+d));return g=(it(y)+"e").split("e"),+(g[0]+"e"+(+g[1]-d))}return o(l)}}var pM=Kn&&1/ha(new Kn([,-0]))[1]==Oe?function(r){return new Kn(r)}:Mc;function Gm(r){return function(o){var l=Gt(o);return l==b?Bu(o):l==K?AT(o):CT(o,r(o))}}function br(r,o,l,d,g,y,E,x){var I=o&T;if(!I&&typeof r!="function")throw new xs(u);var L=d?d.length:0;if(L||(o&=~(Q|he),d=g=s),E=E===s?E:Mt(Le(E),0),x=x===s?x:Le(x),L-=g?g.length:0,o&he){var B=d,H=g;d=g=s}var ue=I?s:hc(r),ve=[r,o,l,d,g,B,H,y,E,x];if(ue&&NM(ve,ue),r=ve[0],o=ve[1],l=ve[2],d=ve[3],g=ve[4],x=ve[9]=ve[9]===s?I?0:r.length:Mt(ve[9]-L,0),!x&&o&(G|we)&&(o&=~(G|we)),!o||o==te)var Ie=dM(r,o,l);else o==G||o==we?Ie=fM(r,o,x):(o==Q||o==(te|Q))&&!g.length?Ie=hM(r,o,l,d):Ie=Ra.apply(s,ve);var je=ue?Om:ng;return og(je(Ie,ve),r,o)}function Km(r,o,l,d){return r===s||$s(r,Gn[l])&&!lt.call(d,l)?o:r}function Ym(r,o,l,d,g,y){return _t(r)&&_t(o)&&(y.set(o,r),Aa(r,o,s,Ym,y),y.delete(o)),r}function mM(r){return ni(r)?s:r}function Qm(r,o,l,d,g,y){var E=l&k,x=r.length,I=o.length;if(x!=I&&!(E&&I>x))return!1;var L=y.get(r),B=y.get(o);if(L&&B)return L==o&&B==r;var H=-1,ue=!0,ve=l&U?new yn:s;for(y.set(r,o),y.set(o,r);++H<x;){var Ie=r[H],je=o[H];if(d)var Ne=E?d(je,Ie,H,o,r,y):d(Ie,je,H,r,o,y);if(Ne!==s){if(Ne)continue;ue=!1;break}if(ve){if(!ku(o,function(Ge,Qe){if(!qo(ve,Qe)&&(Ie===Ge||g(Ie,Ge,l,d,y)))return ve.push(Qe)})){ue=!1;break}}else if(!(Ie===je||g(Ie,je,l,d,y))){ue=!1;break}}return y.delete(r),y.delete(o),ue}function gM(r,o,l,d,g,y,E){switch(l){case Ue:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case De:return!(r.byteLength!=o.byteLength||!y(new ya(r),new ya(o)));case oe:case ne:case C:return $s(+r,+o);case Re:return r.name==o.name&&r.message==o.message;case ee:case Z:return r==o+"";case b:var x=Bu;case K:var I=d&k;if(x||(x=ha),r.size!=o.size&&!I)return!1;var L=E.get(r);if(L)return L==o;d|=U,E.set(r,o);var B=Qm(x(r),x(o),d,g,y,E);return E.delete(r),B;case z:if(Yo)return Yo.call(r)==Yo.call(o)}return!1}function vM(r,o,l,d,g,y){var E=l&k,x=dc(r),I=x.length,L=dc(o),B=L.length;if(I!=B&&!E)return!1;for(var H=I;H--;){var ue=x[H];if(!(E?ue in o:lt.call(o,ue)))return!1}var ve=y.get(r),Ie=y.get(o);if(ve&&Ie)return ve==o&&Ie==r;var je=!0;y.set(r,o),y.set(o,r);for(var Ne=E;++H<I;){ue=x[H];var Ge=r[ue],Qe=o[ue];if(d)var _s=E?d(Qe,Ge,ue,o,r,y):d(Ge,Qe,ue,r,o,y);if(!(_s===s?Ge===Qe||g(Ge,Qe,l,d,y):_s)){je=!1;break}Ne||(Ne=ue=="constructor")}if(je&&!Ne){var rs=r.constructor,ys=o.constructor;rs!=ys&&"constructor"in r&&"constructor"in o&&!(typeof rs=="function"&&rs instanceof rs&&typeof ys=="function"&&ys instanceof ys)&&(je=!1)}return y.delete(r),y.delete(o),je}function wr(r){return yc(sg(r,s,dg),r+"")}function dc(r){return gm(r,Rt,mc)}function fc(r){return gm(r,is,Zm)}var hc=xa?function(r){return xa.get(r)}:Mc;function La(r){for(var o=r.name+"",l=Yn[o],d=lt.call(Yn,o)?l.length:0;d--;){var g=l[d],y=g.func;if(y==null||y==r)return g.name}return o}function Xn(r){var o=lt.call(_,"placeholder")?_:r;return o.placeholder}function xe(){var r=_.iteratee||Tc;return r=r===Tc?ym:r,arguments.length?r(arguments[0],arguments[1]):r}function Ba(r,o){var l=r.__data__;return xM(o)?l[typeof o=="string"?"string":"hash"]:l.map}function pc(r){for(var o=Rt(r),l=o.length;l--;){var d=o[l],g=r[d];o[l]=[d,g,eg(g)]}return o}function En(r,o){var l=IT(r,o);return _m(l)?l:s}function _M(r){var o=lt.call(r,vn),l=r[vn];try{r[vn]=s;var d=!0}catch{}var g=va.call(r);return d&&(o?r[vn]=l:delete r[vn]),g}var mc=ju?function(r){return r==null?[]:(r=ct(r),Br(ju(r),function(o){return rm.call(r,o)}))}:Pc,Zm=ju?function(r){for(var o=[];r;)$r(o,mc(r)),r=ba(r);return o}:Pc,Gt=ts;(Hu&&Gt(new Hu(new ArrayBuffer(1)))!=Ue||Wo&&Gt(new Wo)!=b||qu&&Gt(qu.resolve())!=j||Kn&&Gt(new Kn)!=K||Go&&Gt(new Go)!=re)&&(Gt=function(r){var o=ts(r),l=o==F?r.constructor:s,d=l?Cn(l):"";if(d)switch(d){case eA:return Ue;case tA:return b;case sA:return j;case rA:return K;case nA:return re}return o});function yM(r,o,l){for(var d=-1,g=l.length;++d<g;){var y=l[d],E=y.size;switch(y.type){case"drop":r+=E;break;case"dropRight":o-=E;break;case"take":o=Wt(o,r+E);break;case"takeRight":r=Mt(r,o-E);break}}return{start:r,end:o}}function bM(r){var o=r.match(SN);return o?o[1].split(ON):[]}function Jm(r,o,l){o=Wr(o,r);for(var d=-1,g=o.length,y=!1;++d<g;){var E=Xs(o[d]);if(!(y=r!=null&&l(r,E)))break;r=r[E]}return y||++d!=g?y:(g=r==null?0:r.length,!!g&&Ga(g)&&Er(E,g)&&(Fe(r)||Dn(r)))}function wM(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&lt.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function Xm(r){return typeof r.constructor=="function"&&!si(r)?Qn(ba(r)):{}}function EM(r,o,l){var d=r.constructor;switch(o){case De:return lc(r);case oe:case ne:return new d(+r);case Ue:return oM(r,l);case st:case Je:case Lt:case St:case es:case Bt:case gr:case $n:case Tt:return Rm(r,l);case b:return new d;case C:case Z:return new d(r);case ee:return iM(r);case K:return new d;case z:return aM(r)}}function CM(r,o){var l=o.length;if(!l)return r;var d=l-1;return o[d]=(l>1?"& ":"")+o[d],o=o.join(l>2?", ":" "),r.replace(xN,`{
/* [wrapped with `+o+`] */
`)}function DM(r){return Fe(r)||Dn(r)||!!(nm&&r&&r[nm])}function Er(r,o){var l=typeof r;return o=o??Ee,!!o&&(l=="number"||l!="symbol"&&VN.test(r))&&r>-1&&r%1==0&&r<o}function ss(r,o,l){if(!_t(l))return!1;var d=typeof o;return(d=="number"?os(l)&&Er(o,l.length):d=="string"&&o in l)?$s(l[o],r):!1}function gc(r,o){if(Fe(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||vs(r)?!0:wN.test(r)||!bN.test(r)||o!=null&&r in ct(o)}function xM(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function vc(r){var o=La(r),l=_[o];if(typeof l!="function"||!(o in Ye.prototype))return!1;if(r===l)return!0;var d=hc(l);return!!d&&r===d[0]}function SM(r){return!!em&&em in r}var OM=ma?Cr:kc;function si(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||Gn;return r===l}function eg(r){return r===r&&!_t(r)}function tg(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in ct(l))}}function IM(r){var o=za(r,function(d){return l.size===m&&l.clear(),d}),l=o.cache;return o}function NM(r,o){var l=r[1],d=o[1],g=l|d,y=g<(te|T|be),E=d==be&&l==G||d==be&&l==Ae&&r[7].length<=o[8]||d==(be|Ae)&&o[7].length<=o[8]&&l==G;if(!(y||E))return r;d&te&&(r[2]=o[2],g|=l&te?0:se);var x=o[3];if(x){var I=r[3];r[3]=I?Fm(I,x,o[4]):x,r[4]=I?jr(r[3],p):o[4]}return x=o[5],x&&(I=r[5],r[5]=I?Um(I,x,o[6]):x,r[6]=I?jr(r[5],p):o[6]),x=o[7],x&&(r[7]=x),d&be&&(r[8]=r[8]==null?o[8]:Wt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=g,r}function TM(r){var o=[];if(r!=null)for(var l in ct(r))o.push(l);return o}function AM(r){return va.call(r)}function sg(r,o,l){return o=Mt(o===s?r.length-1:o,0),function(){for(var d=arguments,g=-1,y=Mt(d.length-o,0),E=M(y);++g<y;)E[g]=d[o+g];g=-1;for(var x=M(o+1);++g<o;)x[g]=d[g];return x[o]=l(E),ps(r,this,x)}}function rg(r,o){return o.length<2?r:wn(r,Is(o,0,-1))}function MM(r,o){for(var l=r.length,d=Wt(o.length,l),g=ns(r);d--;){var y=o[d];r[d]=Er(y,l)?g[y]:s}return r}function _c(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var ng=ig(Om),ri=GT||function(r,o){return $t.setTimeout(r,o)},yc=ig(tM);function og(r,o,l){var d=o+"";return yc(r,CM(d,PM(bM(d),l)))}function ig(r){var o=0,l=0;return function(){var d=ZT(),g=We-(d-l);if(l=d,g>0){if(++o>=ae)return arguments[0]}else o=0;return r.apply(s,arguments)}}function $a(r,o){var l=-1,d=r.length,g=d-1;for(o=o===s?d:o;++l<o;){var y=tc(l,g),E=r[y];r[y]=r[l],r[l]=E}return r.length=o,r}var ag=IM(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(EN,function(l,d,g,y){o.push(g?y.replace(TN,"$1"):d||l)}),o});function Xs(r){if(typeof r=="string"||vs(r))return r;var o=r+"";return o=="0"&&1/r==-Oe?"-0":o}function Cn(r){if(r!=null){try{return ga.call(r)}catch{}try{return r+""}catch{}}return""}function PM(r,o){return Ds(fs,function(l){var d="_."+l[0];o&l[1]&&!da(r,d)&&r.push(d)}),r.sort()}function lg(r){if(r instanceof Ye)return r.clone();var o=new Ss(r.__wrapped__,r.__chain__);return o.__actions__=ns(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function kM(r,o,l){(l?ss(r,o,l):o===s)?o=1:o=Mt(Le(o),0);var d=r==null?0:r.length;if(!d||o<1)return[];for(var g=0,y=0,E=M(Ca(d/o));g<d;)E[y++]=Is(r,g,g+=o);return E}function RM(r){for(var o=-1,l=r==null?0:r.length,d=0,g=[];++o<l;){var y=r[o];y&&(g[d++]=y)}return g}function VM(){var r=arguments.length;if(!r)return[];for(var o=M(r-1),l=arguments[0],d=r;d--;)o[d-1]=arguments[d];return $r(Fe(l)?ns(l):[l],jt(o,1))}var FM=qe(function(r,o){return Et(r)?Zo(r,jt(o,1,Et,!0)):[]}),UM=qe(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Zo(r,jt(o,1,Et,!0),xe(l,2)):[]}),LM=qe(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Zo(r,jt(o,1,Et,!0),s,l):[]});function BM(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Le(o),Is(r,o<0?0:o,d)):[]}function $M(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Le(o),o=d-o,Is(r,0,o<0?0:o)):[]}function jM(r,o){return r&&r.length?Pa(r,xe(o,3),!0,!0):[]}function HM(r,o){return r&&r.length?Pa(r,xe(o,3),!0):[]}function qM(r,o,l,d){var g=r==null?0:r.length;return g?(l&&typeof l!="number"&&ss(r,o,l)&&(l=0,d=g),VA(r,o,l,d)):[]}function ug(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Le(l);return g<0&&(g=Mt(d+g,0)),fa(r,xe(o,3),g)}function cg(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=d-1;return l!==s&&(g=Le(l),g=l<0?Mt(d+g,0):Wt(g,d-1)),fa(r,xe(o,3),g,!0)}function dg(r){var o=r==null?0:r.length;return o?jt(r,1):[]}function zM(r){var o=r==null?0:r.length;return o?jt(r,Oe):[]}function WM(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:Le(o),jt(r,o)):[]}function GM(r){for(var o=-1,l=r==null?0:r.length,d={};++o<l;){var g=r[o];d[g[0]]=g[1]}return d}function fg(r){return r&&r.length?r[0]:s}function KM(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Le(l);return g<0&&(g=Mt(d+g,0)),Hn(r,o,g)}function YM(r){var o=r==null?0:r.length;return o?Is(r,0,-1):[]}var QM=qe(function(r){var o=gt(r,ic);return o.length&&o[0]===r[0]?Qu(o):[]}),ZM=qe(function(r){var o=Ns(r),l=gt(r,ic);return o===Ns(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Qu(l,xe(o,2)):[]}),JM=qe(function(r){var o=Ns(r),l=gt(r,ic);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Qu(l,s,o):[]});function XM(r,o){return r==null?"":YT.call(r,o)}function Ns(r){var o=r==null?0:r.length;return o?r[o-1]:s}function e2(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=d;return l!==s&&(g=Le(l),g=g<0?Mt(d+g,0):Wt(g,d-1)),o===o?PT(r,o,g):fa(r,Wp,g,!0)}function t2(r,o){return r&&r.length?Cm(r,Le(o)):s}var s2=qe(hg);function hg(r,o){return r&&r.length&&o&&o.length?ec(r,o):r}function r2(r,o,l){return r&&r.length&&o&&o.length?ec(r,o,xe(l,2)):r}function n2(r,o,l){return r&&r.length&&o&&o.length?ec(r,o,s,l):r}var o2=wr(function(r,o){var l=r==null?0:r.length,d=Wu(r,o);return Sm(r,gt(o,function(g){return Er(g,l)?+g:g}).sort(Vm)),d});function i2(r,o){var l=[];if(!(r&&r.length))return l;var d=-1,g=[],y=r.length;for(o=xe(o,3);++d<y;){var E=r[d];o(E,d,r)&&(l.push(E),g.push(d))}return Sm(r,g),l}function bc(r){return r==null?r:XT.call(r)}function a2(r,o,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&ss(r,o,l)?(o=0,l=d):(o=o==null?0:Le(o),l=l===s?d:Le(l)),Is(r,o,l)):[]}function l2(r,o){return Ma(r,o)}function u2(r,o,l){return rc(r,o,xe(l,2))}function c2(r,o){var l=r==null?0:r.length;if(l){var d=Ma(r,o);if(d<l&&$s(r[d],o))return d}return-1}function d2(r,o){return Ma(r,o,!0)}function f2(r,o,l){return rc(r,o,xe(l,2),!0)}function h2(r,o){var l=r==null?0:r.length;if(l){var d=Ma(r,o,!0)-1;if($s(r[d],o))return d}return-1}function p2(r){return r&&r.length?Im(r):[]}function m2(r,o){return r&&r.length?Im(r,xe(o,2)):[]}function g2(r){var o=r==null?0:r.length;return o?Is(r,1,o):[]}function v2(r,o,l){return r&&r.length?(o=l||o===s?1:Le(o),Is(r,0,o<0?0:o)):[]}function _2(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Le(o),o=d-o,Is(r,o<0?0:o,d)):[]}function y2(r,o){return r&&r.length?Pa(r,xe(o,3),!1,!0):[]}function b2(r,o){return r&&r.length?Pa(r,xe(o,3)):[]}var w2=qe(function(r){return zr(jt(r,1,Et,!0))}),E2=qe(function(r){var o=Ns(r);return Et(o)&&(o=s),zr(jt(r,1,Et,!0),xe(o,2))}),C2=qe(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,zr(jt(r,1,Et,!0),s,o)});function D2(r){return r&&r.length?zr(r):[]}function x2(r,o){return r&&r.length?zr(r,xe(o,2)):[]}function S2(r,o){return o=typeof o=="function"?o:s,r&&r.length?zr(r,s,o):[]}function wc(r){if(!(r&&r.length))return[];var o=0;return r=Br(r,function(l){if(Et(l))return o=Mt(l.length,o),!0}),Uu(o,function(l){return gt(r,Ru(l))})}function pg(r,o){if(!(r&&r.length))return[];var l=wc(r);return o==null?l:gt(l,function(d){return ps(o,s,d)})}var O2=qe(function(r,o){return Et(r)?Zo(r,o):[]}),I2=qe(function(r){return oc(Br(r,Et))}),N2=qe(function(r){var o=Ns(r);return Et(o)&&(o=s),oc(Br(r,Et),xe(o,2))}),T2=qe(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,oc(Br(r,Et),s,o)}),A2=qe(wc);function M2(r,o){return Mm(r||[],o||[],Qo)}function P2(r,o){return Mm(r||[],o||[],ei)}var k2=qe(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,pg(r,l)});function mg(r){var o=_(r);return o.__chain__=!0,o}function R2(r,o){return o(r),r}function ja(r,o){return o(r)}var V2=wr(function(r){var o=r.length,l=o?r[0]:0,d=this.__wrapped__,g=function(y){return Wu(y,r)};return o>1||this.__actions__.length||!(d instanceof Ye)||!Er(l)?this.thru(g):(d=d.slice(l,+l+(o?1:0)),d.__actions__.push({func:ja,args:[g],thisArg:s}),new Ss(d,this.__chain__).thru(function(y){return o&&!y.length&&y.push(s),y}))});function F2(){return mg(this)}function U2(){return new Ss(this.value(),this.__chain__)}function L2(){this.__values__===s&&(this.__values__=Ng(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function B2(){return this}function $2(r){for(var o,l=this;l instanceof Oa;){var d=lg(l);d.__index__=0,d.__values__=s,o?g.__wrapped__=d:o=d;var g=d;l=l.__wrapped__}return g.__wrapped__=r,o}function j2(){var r=this.__wrapped__;if(r instanceof Ye){var o=r;return this.__actions__.length&&(o=new Ye(this)),o=o.reverse(),o.__actions__.push({func:ja,args:[bc],thisArg:s}),new Ss(o,this.__chain__)}return this.thru(bc)}function H2(){return Am(this.__wrapped__,this.__actions__)}var q2=ka(function(r,o,l){lt.call(r,l)?++r[l]:yr(r,l,1)});function z2(r,o,l){var d=Fe(r)?qp:RA;return l&&ss(r,o,l)&&(o=s),d(r,xe(o,3))}function W2(r,o){var l=Fe(r)?Br:pm;return l(r,xe(o,3))}var G2=jm(ug),K2=jm(cg);function Y2(r,o){return jt(Ha(r,o),1)}function Q2(r,o){return jt(Ha(r,o),Oe)}function Z2(r,o,l){return l=l===s?1:Le(l),jt(Ha(r,o),l)}function gg(r,o){var l=Fe(r)?Ds:qr;return l(r,xe(o,3))}function vg(r,o){var l=Fe(r)?gT:hm;return l(r,xe(o,3))}var J2=ka(function(r,o,l){lt.call(r,l)?r[l].push(o):yr(r,l,[o])});function X2(r,o,l,d){r=os(r)?r:to(r),l=l&&!d?Le(l):0;var g=r.length;return l<0&&(l=Mt(g+l,0)),Ka(r)?l<=g&&r.indexOf(o,l)>-1:!!g&&Hn(r,o,l)>-1}var eP=qe(function(r,o,l){var d=-1,g=typeof o=="function",y=os(r)?M(r.length):[];return qr(r,function(E){y[++d]=g?ps(o,E,l):Jo(E,o,l)}),y}),tP=ka(function(r,o,l){yr(r,l,o)});function Ha(r,o){var l=Fe(r)?gt:bm;return l(r,xe(o,3))}function sP(r,o,l,d){return r==null?[]:(Fe(o)||(o=o==null?[]:[o]),l=d?s:l,Fe(l)||(l=l==null?[]:[l]),Dm(r,o,l))}var rP=ka(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function nP(r,o,l){var d=Fe(r)?Pu:Kp,g=arguments.length<3;return d(r,xe(o,4),l,g,qr)}function oP(r,o,l){var d=Fe(r)?vT:Kp,g=arguments.length<3;return d(r,xe(o,4),l,g,hm)}function iP(r,o){var l=Fe(r)?Br:pm;return l(r,Wa(xe(o,3)))}function aP(r){var o=Fe(r)?um:XA;return o(r)}function lP(r,o,l){(l?ss(r,o,l):o===s)?o=1:o=Le(o);var d=Fe(r)?TA:eM;return d(r,o)}function uP(r){var o=Fe(r)?AA:sM;return o(r)}function cP(r){if(r==null)return 0;if(os(r))return Ka(r)?zn(r):r.length;var o=Gt(r);return o==b||o==K?r.size:Ju(r).length}function dP(r,o,l){var d=Fe(r)?ku:rM;return l&&ss(r,o,l)&&(o=s),d(r,xe(o,3))}var fP=qe(function(r,o){if(r==null)return[];var l=o.length;return l>1&&ss(r,o[0],o[1])?o=[]:l>2&&ss(o[0],o[1],o[2])&&(o=[o[0]]),Dm(r,jt(o,1),[])}),qa=WT||function(){return $t.Date.now()};function hP(r,o){if(typeof o!="function")throw new xs(u);return r=Le(r),function(){if(--r<1)return o.apply(this,arguments)}}function _g(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,br(r,be,s,s,s,s,o)}function yg(r,o){var l;if(typeof o!="function")throw new xs(u);return r=Le(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var Ec=qe(function(r,o,l){var d=te;if(l.length){var g=jr(l,Xn(Ec));d|=Q}return br(r,d,o,l,g)}),bg=qe(function(r,o,l){var d=te|T;if(l.length){var g=jr(l,Xn(bg));d|=Q}return br(o,d,r,l,g)});function wg(r,o,l){o=l?s:o;var d=br(r,G,s,s,s,s,s,o);return d.placeholder=wg.placeholder,d}function Eg(r,o,l){o=l?s:o;var d=br(r,we,s,s,s,s,s,o);return d.placeholder=Eg.placeholder,d}function Cg(r,o,l){var d,g,y,E,x,I,L=0,B=!1,H=!1,ue=!0;if(typeof r!="function")throw new xs(u);o=Ts(o)||0,_t(l)&&(B=!!l.leading,H="maxWait"in l,y=H?Mt(Ts(l.maxWait)||0,o):y,ue="trailing"in l?!!l.trailing:ue);function ve(Ct){var js=d,xr=g;return d=g=s,L=Ct,E=r.apply(xr,js),E}function Ie(Ct){return L=Ct,x=ri(Ge,o),B?ve(Ct):E}function je(Ct){var js=Ct-I,xr=Ct-L,jg=o-js;return H?Wt(jg,y-xr):jg}function Ne(Ct){var js=Ct-I,xr=Ct-L;return I===s||js>=o||js<0||H&&xr>=y}function Ge(){var Ct=qa();if(Ne(Ct))return Qe(Ct);x=ri(Ge,je(Ct))}function Qe(Ct){return x=s,ue&&d?ve(Ct):(d=g=s,E)}function _s(){x!==s&&Pm(x),L=0,d=I=g=x=s}function rs(){return x===s?E:Qe(qa())}function ys(){var Ct=qa(),js=Ne(Ct);if(d=arguments,g=this,I=Ct,js){if(x===s)return Ie(I);if(H)return Pm(x),x=ri(Ge,o),ve(I)}return x===s&&(x=ri(Ge,o)),E}return ys.cancel=_s,ys.flush=rs,ys}var pP=qe(function(r,o){return fm(r,1,o)}),mP=qe(function(r,o,l){return fm(r,Ts(o)||0,l)});function gP(r){return br(r,le)}function za(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new xs(u);var l=function(){var d=arguments,g=o?o.apply(this,d):d[0],y=l.cache;if(y.has(g))return y.get(g);var E=r.apply(this,d);return l.cache=y.set(g,E)||y,E};return l.cache=new(za.Cache||_r),l}za.Cache=_r;function Wa(r){if(typeof r!="function")throw new xs(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function vP(r){return yg(2,r)}var _P=nM(function(r,o){o=o.length==1&&Fe(o[0])?gt(o[0],ms(xe())):gt(jt(o,1),ms(xe()));var l=o.length;return qe(function(d){for(var g=-1,y=Wt(d.length,l);++g<y;)d[g]=o[g].call(this,d[g]);return ps(r,this,d)})}),Cc=qe(function(r,o){var l=jr(o,Xn(Cc));return br(r,Q,s,o,l)}),Dg=qe(function(r,o){var l=jr(o,Xn(Dg));return br(r,he,s,o,l)}),yP=wr(function(r,o){return br(r,Ae,s,s,s,o)});function bP(r,o){if(typeof r!="function")throw new xs(u);return o=o===s?o:Le(o),qe(r,o)}function wP(r,o){if(typeof r!="function")throw new xs(u);return o=o==null?0:Mt(Le(o),0),qe(function(l){var d=l[o],g=Gr(l,0,o);return d&&$r(g,d),ps(r,this,g)})}function EP(r,o,l){var d=!0,g=!0;if(typeof r!="function")throw new xs(u);return _t(l)&&(d="leading"in l?!!l.leading:d,g="trailing"in l?!!l.trailing:g),Cg(r,o,{leading:d,maxWait:o,trailing:g})}function CP(r){return _g(r,1)}function DP(r,o){return Cc(ac(o),r)}function xP(){if(!arguments.length)return[];var r=arguments[0];return Fe(r)?r:[r]}function SP(r){return Os(r,D)}function OP(r,o){return o=typeof o=="function"?o:s,Os(r,D,o)}function IP(r){return Os(r,v|D)}function NP(r,o){return o=typeof o=="function"?o:s,Os(r,v|D,o)}function TP(r,o){return o==null||dm(r,o,Rt(o))}function $s(r,o){return r===o||r!==r&&o!==o}var AP=Ua(Yu),MP=Ua(function(r,o){return r>=o}),Dn=vm(function(){return arguments}())?vm:function(r){return bt(r)&&lt.call(r,"callee")&&!rm.call(r,"callee")},Fe=M.isArray,PP=Up?ms(Up):$A;function os(r){return r!=null&&Ga(r.length)&&!Cr(r)}function Et(r){return bt(r)&&os(r)}function kP(r){return r===!0||r===!1||bt(r)&&ts(r)==oe}var Kr=KT||kc,RP=Lp?ms(Lp):jA;function VP(r){return bt(r)&&r.nodeType===1&&!ni(r)}function FP(r){if(r==null)return!0;if(os(r)&&(Fe(r)||typeof r=="string"||typeof r.splice=="function"||Kr(r)||eo(r)||Dn(r)))return!r.length;var o=Gt(r);if(o==b||o==K)return!r.size;if(si(r))return!Ju(r).length;for(var l in r)if(lt.call(r,l))return!1;return!0}function UP(r,o){return Xo(r,o)}function LP(r,o,l){l=typeof l=="function"?l:s;var d=l?l(r,o):s;return d===s?Xo(r,o,s,l):!!d}function Dc(r){if(!bt(r))return!1;var o=ts(r);return o==Re||o==me||typeof r.message=="string"&&typeof r.name=="string"&&!ni(r)}function BP(r){return typeof r=="number"&&om(r)}function Cr(r){if(!_t(r))return!1;var o=ts(r);return o==at||o==Ve||o==R||o==q}function xg(r){return typeof r=="number"&&r==Le(r)}function Ga(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=Ee}function _t(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function bt(r){return r!=null&&typeof r=="object"}var Sg=Bp?ms(Bp):qA;function $P(r,o){return r===o||Zu(r,o,pc(o))}function jP(r,o,l){return l=typeof l=="function"?l:s,Zu(r,o,pc(o),l)}function HP(r){return Og(r)&&r!=+r}function qP(r){if(OM(r))throw new Pe(a);return _m(r)}function zP(r){return r===null}function WP(r){return r==null}function Og(r){return typeof r=="number"||bt(r)&&ts(r)==C}function ni(r){if(!bt(r)||ts(r)!=F)return!1;var o=ba(r);if(o===null)return!0;var l=lt.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&ga.call(l)==jT}var xc=$p?ms($p):zA;function GP(r){return xg(r)&&r>=-Ee&&r<=Ee}var Ig=jp?ms(jp):WA;function Ka(r){return typeof r=="string"||!Fe(r)&&bt(r)&&ts(r)==Z}function vs(r){return typeof r=="symbol"||bt(r)&&ts(r)==z}var eo=Hp?ms(Hp):GA;function KP(r){return r===s}function YP(r){return bt(r)&&Gt(r)==re}function QP(r){return bt(r)&&ts(r)==_e}var ZP=Ua(Xu),JP=Ua(function(r,o){return r<=o});function Ng(r){if(!r)return[];if(os(r))return Ka(r)?Ls(r):ns(r);if(zo&&r[zo])return TT(r[zo]());var o=Gt(r),l=o==b?Bu:o==K?ha:to;return l(r)}function Dr(r){if(!r)return r===0?r:0;if(r=Ts(r),r===Oe||r===-Oe){var o=r<0?-1:1;return o*Ut}return r===r?r:0}function Le(r){var o=Dr(r),l=o%1;return o===o?l?o-l:o:0}function Tg(r){return r?bn(Le(r),0,yt):0}function Ts(r){if(typeof r=="number")return r;if(vs(r))return Xt;if(_t(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=_t(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=Yp(r);var l=PN.test(r);return l||RN.test(r)?hT(r.slice(2),l?2:8):MN.test(r)?Xt:+r}function Ag(r){return Js(r,is(r))}function XP(r){return r?bn(Le(r),-Ee,Ee):r===0?r:0}function it(r){return r==null?"":gs(r)}var ek=Zn(function(r,o){if(si(o)||os(o)){Js(o,Rt(o),r);return}for(var l in o)lt.call(o,l)&&Qo(r,l,o[l])}),Mg=Zn(function(r,o){Js(o,is(o),r)}),Ya=Zn(function(r,o,l,d){Js(o,is(o),r,d)}),tk=Zn(function(r,o,l,d){Js(o,Rt(o),r,d)}),sk=wr(Wu);function rk(r,o){var l=Qn(r);return o==null?l:cm(l,o)}var nk=qe(function(r,o){r=ct(r);var l=-1,d=o.length,g=d>2?o[2]:s;for(g&&ss(o[0],o[1],g)&&(d=1);++l<d;)for(var y=o[l],E=is(y),x=-1,I=E.length;++x<I;){var L=E[x],B=r[L];(B===s||$s(B,Gn[L])&&!lt.call(r,L))&&(r[L]=y[L])}return r}),ok=qe(function(r){return r.push(s,Ym),ps(Pg,s,r)});function ik(r,o){return zp(r,xe(o,3),Zs)}function ak(r,o){return zp(r,xe(o,3),Ku)}function lk(r,o){return r==null?r:Gu(r,xe(o,3),is)}function uk(r,o){return r==null?r:mm(r,xe(o,3),is)}function ck(r,o){return r&&Zs(r,xe(o,3))}function dk(r,o){return r&&Ku(r,xe(o,3))}function fk(r){return r==null?[]:Ta(r,Rt(r))}function hk(r){return r==null?[]:Ta(r,is(r))}function Sc(r,o,l){var d=r==null?s:wn(r,o);return d===s?l:d}function pk(r,o){return r!=null&&Jm(r,o,FA)}function Oc(r,o){return r!=null&&Jm(r,o,UA)}var mk=qm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=va.call(o)),r[o]=l},Nc(as)),gk=qm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=va.call(o)),lt.call(r,o)?r[o].push(l):r[o]=[l]},xe),vk=qe(Jo);function Rt(r){return os(r)?lm(r):Ju(r)}function is(r){return os(r)?lm(r,!0):KA(r)}function _k(r,o){var l={};return o=xe(o,3),Zs(r,function(d,g,y){yr(l,o(d,g,y),d)}),l}function yk(r,o){var l={};return o=xe(o,3),Zs(r,function(d,g,y){yr(l,g,o(d,g,y))}),l}var bk=Zn(function(r,o,l){Aa(r,o,l)}),Pg=Zn(function(r,o,l,d){Aa(r,o,l,d)}),wk=wr(function(r,o){var l={};if(r==null)return l;var d=!1;o=gt(o,function(y){return y=Wr(y,r),d||(d=y.length>1),y}),Js(r,fc(r),l),d&&(l=Os(l,v|w|D,mM));for(var g=o.length;g--;)nc(l,o[g]);return l});function Ek(r,o){return kg(r,Wa(xe(o)))}var Ck=wr(function(r,o){return r==null?{}:QA(r,o)});function kg(r,o){if(r==null)return{};var l=gt(fc(r),function(d){return[d]});return o=xe(o),xm(r,l,function(d,g){return o(d,g[0])})}function Dk(r,o,l){o=Wr(o,r);var d=-1,g=o.length;for(g||(g=1,r=s);++d<g;){var y=r==null?s:r[Xs(o[d])];y===s&&(d=g,y=l),r=Cr(y)?y.call(r):y}return r}function xk(r,o,l){return r==null?r:ei(r,o,l)}function Sk(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:ei(r,o,l,d)}var Rg=Gm(Rt),Vg=Gm(is);function Ok(r,o,l){var d=Fe(r),g=d||Kr(r)||eo(r);if(o=xe(o,4),l==null){var y=r&&r.constructor;g?l=d?new y:[]:_t(r)?l=Cr(y)?Qn(ba(r)):{}:l={}}return(g?Ds:Zs)(r,function(E,x,I){return o(l,E,x,I)}),l}function Ik(r,o){return r==null?!0:nc(r,o)}function Nk(r,o,l){return r==null?r:Tm(r,o,ac(l))}function Tk(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:Tm(r,o,ac(l),d)}function to(r){return r==null?[]:Lu(r,Rt(r))}function Ak(r){return r==null?[]:Lu(r,is(r))}function Mk(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Ts(l),l=l===l?l:0),o!==s&&(o=Ts(o),o=o===o?o:0),bn(Ts(r),o,l)}function Pk(r,o,l){return o=Dr(o),l===s?(l=o,o=0):l=Dr(l),r=Ts(r),LA(r,o,l)}function kk(r,o,l){if(l&&typeof l!="boolean"&&ss(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=Dr(r),o===s?(o=r,r=0):o=Dr(o)),r>o){var d=r;r=o,o=d}if(l||r%1||o%1){var g=im();return Wt(r+g*(o-r+fT("1e-"+((g+"").length-1))),o)}return tc(r,o)}var Rk=Jn(function(r,o,l){return o=o.toLowerCase(),r+(l?Fg(o):o)});function Fg(r){return Ic(it(r).toLowerCase())}function Ug(r){return r=it(r),r&&r.replace(FN,xT).replace(sT,"")}function Vk(r,o,l){r=it(r),o=gs(o);var d=r.length;l=l===s?d:bn(Le(l),0,d);var g=l;return l-=o.length,l>=0&&r.slice(l,g)==o}function Fk(r){return r=it(r),r&&vN.test(r)?r.replace(mp,ST):r}function Uk(r){return r=it(r),r&&CN.test(r)?r.replace(Cu,"\\$&"):r}var Lk=Jn(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),Bk=Jn(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),$k=$m("toLowerCase");function jk(r,o,l){r=it(r),o=Le(o);var d=o?zn(r):0;if(!o||d>=o)return r;var g=(o-d)/2;return Fa(Da(g),l)+r+Fa(Ca(g),l)}function Hk(r,o,l){r=it(r),o=Le(o);var d=o?zn(r):0;return o&&d<o?r+Fa(o-d,l):r}function qk(r,o,l){r=it(r),o=Le(o);var d=o?zn(r):0;return o&&d<o?Fa(o-d,l)+r:r}function zk(r,o,l){return l||o==null?o=0:o&&(o=+o),JT(it(r).replace(Du,""),o||0)}function Wk(r,o,l){return(l?ss(r,o,l):o===s)?o=1:o=Le(o),sc(it(r),o)}function Gk(){var r=arguments,o=it(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var Kk=Jn(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function Yk(r,o,l){return l&&typeof l!="number"&&ss(r,o,l)&&(o=l=s),l=l===s?yt:l>>>0,l?(r=it(r),r&&(typeof o=="string"||o!=null&&!xc(o))&&(o=gs(o),!o&&qn(r))?Gr(Ls(r),0,l):r.split(o,l)):[]}var Qk=Jn(function(r,o,l){return r+(l?" ":"")+Ic(o)});function Zk(r,o,l){return r=it(r),l=l==null?0:bn(Le(l),0,r.length),o=gs(o),r.slice(l,l+o.length)==o}function Jk(r,o,l){var d=_.templateSettings;l&&ss(r,o,l)&&(o=s),r=it(r),o=Ya({},o,d,Km);var g=Ya({},o.imports,d.imports,Km),y=Rt(g),E=Lu(g,y),x,I,L=0,B=o.interpolate||la,H="__p += '",ue=$u((o.escape||la).source+"|"+B.source+"|"+(B===gp?AN:la).source+"|"+(o.evaluate||la).source+"|$","g"),ve="//# sourceURL="+(lt.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++aT+"]")+`
`;r.replace(ue,function(Ne,Ge,Qe,_s,rs,ys){return Qe||(Qe=_s),H+=r.slice(L,ys).replace(UN,OT),Ge&&(x=!0,H+=`' +
__e(`+Ge+`) +
'`),rs&&(I=!0,H+=`';
`+rs+`;
__p += '`),Qe&&(H+=`' +
((__t = (`+Qe+`)) == null ? '' : __t) +
'`),L=ys+Ne.length,Ne}),H+=`';
`;var Ie=lt.call(o,"variable")&&o.variable;if(!Ie)H=`with (obj) {
`+H+`
}
`;else if(NN.test(Ie))throw new Pe(c);H=(I?H.replace(Es,""):H).replace(aa,"$1").replace(mN,"$1;"),H="function("+(Ie||"obj")+`) {
`+(Ie?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(x?", __e = _.escape":"")+(I?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+H+`return __p
}`;var je=Bg(function(){return rt(y,ve+"return "+H).apply(s,E)});if(je.source=H,Dc(je))throw je;return je}function Xk(r){return it(r).toLowerCase()}function eR(r){return it(r).toUpperCase()}function tR(r,o,l){if(r=it(r),r&&(l||o===s))return Yp(r);if(!r||!(o=gs(o)))return r;var d=Ls(r),g=Ls(o),y=Qp(d,g),E=Zp(d,g)+1;return Gr(d,y,E).join("")}function sR(r,o,l){if(r=it(r),r&&(l||o===s))return r.slice(0,Xp(r)+1);if(!r||!(o=gs(o)))return r;var d=Ls(r),g=Zp(d,Ls(o))+1;return Gr(d,0,g).join("")}function rR(r,o,l){if(r=it(r),r&&(l||o===s))return r.replace(Du,"");if(!r||!(o=gs(o)))return r;var d=Ls(r),g=Qp(d,Ls(o));return Gr(d,g).join("")}function nR(r,o){var l=ie,d=ke;if(_t(o)){var g="separator"in o?o.separator:g;l="length"in o?Le(o.length):l,d="omission"in o?gs(o.omission):d}r=it(r);var y=r.length;if(qn(r)){var E=Ls(r);y=E.length}if(l>=y)return r;var x=l-zn(d);if(x<1)return d;var I=E?Gr(E,0,x).join(""):r.slice(0,x);if(g===s)return I+d;if(E&&(x+=I.length-x),xc(g)){if(r.slice(x).search(g)){var L,B=I;for(g.global||(g=$u(g.source,it(vp.exec(g))+"g")),g.lastIndex=0;L=g.exec(B);)var H=L.index;I=I.slice(0,H===s?x:H)}}else if(r.indexOf(gs(g),x)!=x){var ue=I.lastIndexOf(g);ue>-1&&(I=I.slice(0,ue))}return I+d}function oR(r){return r=it(r),r&&gN.test(r)?r.replace(pp,kT):r}var iR=Jn(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),Ic=$m("toUpperCase");function Lg(r,o,l){return r=it(r),o=l?s:o,o===s?NT(r)?FT(r):bT(r):r.match(o)||[]}var Bg=qe(function(r,o){try{return ps(r,s,o)}catch(l){return Dc(l)?l:new Pe(l)}}),aR=wr(function(r,o){return Ds(o,function(l){l=Xs(l),yr(r,l,Ec(r[l],r))}),r});function lR(r){var o=r==null?0:r.length,l=xe();return r=o?gt(r,function(d){if(typeof d[1]!="function")throw new xs(u);return[l(d[0]),d[1]]}):[],qe(function(d){for(var g=-1;++g<o;){var y=r[g];if(ps(y[0],this,d))return ps(y[1],this,d)}})}function uR(r){return kA(Os(r,v))}function Nc(r){return function(){return r}}function cR(r,o){return r==null||r!==r?o:r}var dR=Hm(),fR=Hm(!0);function as(r){return r}function Tc(r){return ym(typeof r=="function"?r:Os(r,v))}function hR(r){return wm(Os(r,v))}function pR(r,o){return Em(r,Os(o,v))}var mR=qe(function(r,o){return function(l){return Jo(l,r,o)}}),gR=qe(function(r,o){return function(l){return Jo(r,l,o)}});function Ac(r,o,l){var d=Rt(o),g=Ta(o,d);l==null&&!(_t(o)&&(g.length||!d.length))&&(l=o,o=r,r=this,g=Ta(o,Rt(o)));var y=!(_t(l)&&"chain"in l)||!!l.chain,E=Cr(r);return Ds(g,function(x){var I=o[x];r[x]=I,E&&(r.prototype[x]=function(){var L=this.__chain__;if(y||L){var B=r(this.__wrapped__),H=B.__actions__=ns(this.__actions__);return H.push({func:I,args:arguments,thisArg:r}),B.__chain__=L,B}return I.apply(r,$r([this.value()],arguments))})}),r}function vR(){return $t._===this&&($t._=HT),this}function Mc(){}function _R(r){return r=Le(r),qe(function(o){return Cm(o,r)})}var yR=uc(gt),bR=uc(qp),wR=uc(ku);function $g(r){return gc(r)?Ru(Xs(r)):ZA(r)}function ER(r){return function(o){return r==null?s:wn(r,o)}}var CR=zm(),DR=zm(!0);function Pc(){return[]}function kc(){return!1}function xR(){return{}}function SR(){return""}function OR(){return!0}function IR(r,o){if(r=Le(r),r<1||r>Ee)return[];var l=yt,d=Wt(r,yt);o=xe(o),r-=yt;for(var g=Uu(d,o);++l<r;)o(l);return g}function NR(r){return Fe(r)?gt(r,Xs):vs(r)?[r]:ns(ag(it(r)))}function TR(r){var o=++$T;return it(r)+o}var AR=Va(function(r,o){return r+o},0),MR=cc("ceil"),PR=Va(function(r,o){return r/o},1),kR=cc("floor");function RR(r){return r&&r.length?Na(r,as,Yu):s}function VR(r,o){return r&&r.length?Na(r,xe(o,2),Yu):s}function FR(r){return Gp(r,as)}function UR(r,o){return Gp(r,xe(o,2))}function LR(r){return r&&r.length?Na(r,as,Xu):s}function BR(r,o){return r&&r.length?Na(r,xe(o,2),Xu):s}var $R=Va(function(r,o){return r*o},1),jR=cc("round"),HR=Va(function(r,o){return r-o},0);function qR(r){return r&&r.length?Fu(r,as):0}function zR(r,o){return r&&r.length?Fu(r,xe(o,2)):0}return _.after=hP,_.ary=_g,_.assign=ek,_.assignIn=Mg,_.assignInWith=Ya,_.assignWith=tk,_.at=sk,_.before=yg,_.bind=Ec,_.bindAll=aR,_.bindKey=bg,_.castArray=xP,_.chain=mg,_.chunk=kM,_.compact=RM,_.concat=VM,_.cond=lR,_.conforms=uR,_.constant=Nc,_.countBy=q2,_.create=rk,_.curry=wg,_.curryRight=Eg,_.debounce=Cg,_.defaults=nk,_.defaultsDeep=ok,_.defer=pP,_.delay=mP,_.difference=FM,_.differenceBy=UM,_.differenceWith=LM,_.drop=BM,_.dropRight=$M,_.dropRightWhile=jM,_.dropWhile=HM,_.fill=qM,_.filter=W2,_.flatMap=Y2,_.flatMapDeep=Q2,_.flatMapDepth=Z2,_.flatten=dg,_.flattenDeep=zM,_.flattenDepth=WM,_.flip=gP,_.flow=dR,_.flowRight=fR,_.fromPairs=GM,_.functions=fk,_.functionsIn=hk,_.groupBy=J2,_.initial=YM,_.intersection=QM,_.intersectionBy=ZM,_.intersectionWith=JM,_.invert=mk,_.invertBy=gk,_.invokeMap=eP,_.iteratee=Tc,_.keyBy=tP,_.keys=Rt,_.keysIn=is,_.map=Ha,_.mapKeys=_k,_.mapValues=yk,_.matches=hR,_.matchesProperty=pR,_.memoize=za,_.merge=bk,_.mergeWith=Pg,_.method=mR,_.methodOf=gR,_.mixin=Ac,_.negate=Wa,_.nthArg=_R,_.omit=wk,_.omitBy=Ek,_.once=vP,_.orderBy=sP,_.over=yR,_.overArgs=_P,_.overEvery=bR,_.overSome=wR,_.partial=Cc,_.partialRight=Dg,_.partition=rP,_.pick=Ck,_.pickBy=kg,_.property=$g,_.propertyOf=ER,_.pull=s2,_.pullAll=hg,_.pullAllBy=r2,_.pullAllWith=n2,_.pullAt=o2,_.range=CR,_.rangeRight=DR,_.rearg=yP,_.reject=iP,_.remove=i2,_.rest=bP,_.reverse=bc,_.sampleSize=lP,_.set=xk,_.setWith=Sk,_.shuffle=uP,_.slice=a2,_.sortBy=fP,_.sortedUniq=p2,_.sortedUniqBy=m2,_.split=Yk,_.spread=wP,_.tail=g2,_.take=v2,_.takeRight=_2,_.takeRightWhile=y2,_.takeWhile=b2,_.tap=R2,_.throttle=EP,_.thru=ja,_.toArray=Ng,_.toPairs=Rg,_.toPairsIn=Vg,_.toPath=NR,_.toPlainObject=Ag,_.transform=Ok,_.unary=CP,_.union=w2,_.unionBy=E2,_.unionWith=C2,_.uniq=D2,_.uniqBy=x2,_.uniqWith=S2,_.unset=Ik,_.unzip=wc,_.unzipWith=pg,_.update=Nk,_.updateWith=Tk,_.values=to,_.valuesIn=Ak,_.without=O2,_.words=Lg,_.wrap=DP,_.xor=I2,_.xorBy=N2,_.xorWith=T2,_.zip=A2,_.zipObject=M2,_.zipObjectDeep=P2,_.zipWith=k2,_.entries=Rg,_.entriesIn=Vg,_.extend=Mg,_.extendWith=Ya,Ac(_,_),_.add=AR,_.attempt=Bg,_.camelCase=Rk,_.capitalize=Fg,_.ceil=MR,_.clamp=Mk,_.clone=SP,_.cloneDeep=IP,_.cloneDeepWith=NP,_.cloneWith=OP,_.conformsTo=TP,_.deburr=Ug,_.defaultTo=cR,_.divide=PR,_.endsWith=Vk,_.eq=$s,_.escape=Fk,_.escapeRegExp=Uk,_.every=z2,_.find=G2,_.findIndex=ug,_.findKey=ik,_.findLast=K2,_.findLastIndex=cg,_.findLastKey=ak,_.floor=kR,_.forEach=gg,_.forEachRight=vg,_.forIn=lk,_.forInRight=uk,_.forOwn=ck,_.forOwnRight=dk,_.get=Sc,_.gt=AP,_.gte=MP,_.has=pk,_.hasIn=Oc,_.head=fg,_.identity=as,_.includes=X2,_.indexOf=KM,_.inRange=Pk,_.invoke=vk,_.isArguments=Dn,_.isArray=Fe,_.isArrayBuffer=PP,_.isArrayLike=os,_.isArrayLikeObject=Et,_.isBoolean=kP,_.isBuffer=Kr,_.isDate=RP,_.isElement=VP,_.isEmpty=FP,_.isEqual=UP,_.isEqualWith=LP,_.isError=Dc,_.isFinite=BP,_.isFunction=Cr,_.isInteger=xg,_.isLength=Ga,_.isMap=Sg,_.isMatch=$P,_.isMatchWith=jP,_.isNaN=HP,_.isNative=qP,_.isNil=WP,_.isNull=zP,_.isNumber=Og,_.isObject=_t,_.isObjectLike=bt,_.isPlainObject=ni,_.isRegExp=xc,_.isSafeInteger=GP,_.isSet=Ig,_.isString=Ka,_.isSymbol=vs,_.isTypedArray=eo,_.isUndefined=KP,_.isWeakMap=YP,_.isWeakSet=QP,_.join=XM,_.kebabCase=Lk,_.last=Ns,_.lastIndexOf=e2,_.lowerCase=Bk,_.lowerFirst=$k,_.lt=ZP,_.lte=JP,_.max=RR,_.maxBy=VR,_.mean=FR,_.meanBy=UR,_.min=LR,_.minBy=BR,_.stubArray=Pc,_.stubFalse=kc,_.stubObject=xR,_.stubString=SR,_.stubTrue=OR,_.multiply=$R,_.nth=t2,_.noConflict=vR,_.noop=Mc,_.now=qa,_.pad=jk,_.padEnd=Hk,_.padStart=qk,_.parseInt=zk,_.random=kk,_.reduce=nP,_.reduceRight=oP,_.repeat=Wk,_.replace=Gk,_.result=Dk,_.round=jR,_.runInContext=S,_.sample=aP,_.size=cP,_.snakeCase=Kk,_.some=dP,_.sortedIndex=l2,_.sortedIndexBy=u2,_.sortedIndexOf=c2,_.sortedLastIndex=d2,_.sortedLastIndexBy=f2,_.sortedLastIndexOf=h2,_.startCase=Qk,_.startsWith=Zk,_.subtract=HR,_.sum=qR,_.sumBy=zR,_.template=Jk,_.times=IR,_.toFinite=Dr,_.toInteger=Le,_.toLength=Tg,_.toLower=Xk,_.toNumber=Ts,_.toSafeInteger=XP,_.toString=it,_.toUpper=eR,_.trim=tR,_.trimEnd=sR,_.trimStart=rR,_.truncate=nR,_.unescape=oR,_.uniqueId=TR,_.upperCase=iR,_.upperFirst=Ic,_.each=gg,_.eachRight=vg,_.first=fg,Ac(_,function(){var r={};return Zs(_,function(o,l){lt.call(_.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),_.VERSION=i,Ds(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){_[r].placeholder=_}),Ds(["drop","take"],function(r,o){Ye.prototype[r]=function(l){l=l===s?1:Mt(Le(l),0);var d=this.__filtered__&&!o?new Ye(this):this.clone();return d.__filtered__?d.__takeCount__=Wt(l,d.__takeCount__):d.__views__.push({size:Wt(l,yt),type:r+(d.__dir__<0?"Right":"")}),d},Ye.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Ds(["filter","map","takeWhile"],function(r,o){var l=o+1,d=l==J||l==ut;Ye.prototype[r]=function(g){var y=this.clone();return y.__iteratees__.push({iteratee:xe(g,3),type:l}),y.__filtered__=y.__filtered__||d,y}}),Ds(["head","last"],function(r,o){var l="take"+(o?"Right":"");Ye.prototype[r]=function(){return this[l](1).value()[0]}}),Ds(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");Ye.prototype[r]=function(){return this.__filtered__?new Ye(this):this[l](1)}}),Ye.prototype.compact=function(){return this.filter(as)},Ye.prototype.find=function(r){return this.filter(r).head()},Ye.prototype.findLast=function(r){return this.reverse().find(r)},Ye.prototype.invokeMap=qe(function(r,o){return typeof r=="function"?new Ye(this):this.map(function(l){return Jo(l,r,o)})}),Ye.prototype.reject=function(r){return this.filter(Wa(xe(r)))},Ye.prototype.slice=function(r,o){r=Le(r);var l=this;return l.__filtered__&&(r>0||o<0)?new Ye(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=Le(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},Ye.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},Ye.prototype.toArray=function(){return this.take(yt)},Zs(Ye.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),d=/^(?:head|last)$/.test(o),g=_[d?"take"+(o=="last"?"Right":""):o],y=d||/^find/.test(o);g&&(_.prototype[o]=function(){var E=this.__wrapped__,x=d?[1]:arguments,I=E instanceof Ye,L=x[0],B=I||Fe(E),H=function(Ge){var Qe=g.apply(_,$r([Ge],x));return d&&ue?Qe[0]:Qe};B&&l&&typeof L=="function"&&L.length!=1&&(I=B=!1);var ue=this.__chain__,ve=!!this.__actions__.length,Ie=y&&!ue,je=I&&!ve;if(!y&&B){E=je?E:new Ye(this);var Ne=r.apply(E,x);return Ne.__actions__.push({func:ja,args:[H],thisArg:s}),new Ss(Ne,ue)}return Ie&&je?r.apply(this,x):(Ne=this.thru(H),Ie?d?Ne.value()[0]:Ne.value():Ne)})}),Ds(["pop","push","shift","sort","splice","unshift"],function(r){var o=pa[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);_.prototype[r]=function(){var g=arguments;if(d&&!this.__chain__){var y=this.value();return o.apply(Fe(y)?y:[],g)}return this[l](function(E){return o.apply(Fe(E)?E:[],g)})}}),Zs(Ye.prototype,function(r,o){var l=_[o];if(l){var d=l.name+"";lt.call(Yn,d)||(Yn[d]=[]),Yn[d].push({name:o,func:l})}}),Yn[Ra(s,T).name]=[{name:"wrapper",func:s}],Ye.prototype.clone=oA,Ye.prototype.reverse=iA,Ye.prototype.value=aA,_.prototype.at=V2,_.prototype.chain=F2,_.prototype.commit=U2,_.prototype.next=L2,_.prototype.plant=$2,_.prototype.reverse=j2,_.prototype.toJSON=_.prototype.valueOf=_.prototype.value=H2,_.prototype.first=_.prototype.head,zo&&(_.prototype[zo]=B2),_},Wn=UT();gn?((gn.exports=Wn)._=Wn,Tu._=Wn):$t._=Wn}).call(Fo)}(Xi,Xi.exports);var sp=Xi.exports;const Be=async(e,t)=>{try{const s=window.M.cfg.wwwroot+"/lib/ajax/service.php?sesskey="+window.M.cfg.sesskey+"&info="+e,n=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify([{index:0,methodname:e,args:t}])}),a=n.clone();try{return(await n.json())[0]}catch{return{error:await a.text()}}}catch(s){throw console.error("Erro na chamada AJAX:",s),s}};async function X0(e={}){try{return await Be("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw new Error(t.message||"Erro ao buscar ofertas")}}async function rp(e){try{return await Be("local_offermanager_get",{id:e})}catch(t){throw new Error(t.message||"Erro ao buscar oferta")}}async function np(e){try{return await Be("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",audienceids:e.audiences||[]})}catch(t){throw new Error(t.message||"Erro ao salvar oferta")}}async function e1(e){try{return await Be("local_offermanager_delete",{id:e})}catch(t){throw new Error(t.message||"Erro ao excluir oferta")}}async function pu(){try{return await Be("local_offermanager_get_type_options",{})}catch(e){throw new Error(e.message||"Erro ao buscar opções de tipos")}}async function t1(e,t){try{return await Be("local_offermanager_add_course_to_offer",{offer_id:e,course:t})}catch(s){throw new Error(s.message||"Erro ao adicionar curso à oferta")}}async function s1(e,t){try{return await Be("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw new Error(s.message||"Erro ao remover curso da oferta")}}async function r1(e,t,s){try{return await Be("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw new Error(i.message||"Erro ao alterar status do curso")}}async function op(e){var t;try{const s=await Be("local_offermanager_get_audiences",{offerid:0});return(t=s==null?void 0:s.data)!=null&&t.all_audiences?{items:s.data.all_audiences.filter(n=>n.name.toLowerCase().includes(e.toLowerCase())).map(n=>({id:n.id,name:n.name}))}:{items:[]}}catch(s){throw new Error(s)}}async function n1(e,t){try{return await Be("local_offermanager_update_audiences",{offerid:e,audienceids:t})}catch(s){throw new Error(s)}}async function o1(e,t){try{return await Be("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw new Error(s.message||"Erro ao alterar status da oferta")}}async function Uo(e="",t=0){try{return await Be("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw new Error(s.message||"Erro ao buscar categorias")}}async function ip(e,t,s="",i=1,n=20){try{console.log(`getCoursesByCategory - Parâmetros: offerId=${e}, categoryId=${t}, search=${s}, page=${i}, perPage=${n}`);const a=parseInt(e,10),u=parseInt(t,10),c=parseInt(i,10),h=parseInt(n,10);(isNaN(a)||isNaN(u)||isNaN(c)||isNaN(h))&&console.error("Parâmetros inválidos para getCoursesByCategory");const m={offerid:a,categoryid:u,search_string:s||"",page:c,per_page:h,exclude_courseids:[]};console.log("Chamando endpoint com parâmetros:",m);const p=await Be("local_offermanager_fetch_potential_courses",m);return console.log("Resposta bruta do endpoint:",p),p}catch(a){throw console.error("Erro em getCoursesByCategory:",a),new Error(a.message||"Erro ao buscar cursos")}}async function ap(e,t=""){try{return await Be("local_offermanager_fetch_current_courses",{offerid:e,categoryid:0,search_string:t,exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por nome")}}async function mu(e,t){try{return await Be("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:"",exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por categoria")}}async function i1(e,t){try{return await Be("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw new Error(s.message||"Erro ao adicionar cursos à oferta")}}async function ea(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="turmasCount"&&(t.sortBy="class_counter"),await Be("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw new Error(s.message||"Erro ao buscar cursos da oferta")}}async function a1(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};e.offercourseid?s.offercourseid=parseInt(e.offercourseid):console.error("offercourseid não está definido nos parâmetros"),e.classname?s.classname=e.classname:console.error("classname não está definido nos parâmetros"),e.startdate?s.startdate=e.startdate:console.error("startdate não está definido nos parâmetros"),e.teachers&&Array.isArray(e.teachers)?s.teachers=[...e.teachers]:(console.warn("teachers não está definido nos parâmetros ou não é um array"),s.teachers=[]),e.enrol?s.enrol=e.enrol:console.error("enrol não está definido nos parâmetros"),e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}});const n=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(n.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",n),new Error(`Campos obrigatórios ausentes: ${n.join(", ")}`);return await Be("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),new Error(t.message||"Erro ao criar turma")}}async function Lr(e){try{return await Be("local_offermanager_get_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao buscar turma")}}async function l1(e){try{const t=await Be("local_offermanager_get_course",{offercourseid:e});return t.error?(console.log(t.exception.message),[]):t}catch(t){throw new Error(t.message||"Erro ao buscar curso da oferta")}}async function gu(e){try{const t=await Be("local_offermanager_get_classes",{offercourseid:e});return Array.isArray(t)&&t.length===0?(console.log(`Curso ${e} não tem turmas (array vazio)`),[]):Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data)&&t[0].data.length===0?(console.log(`Curso ${e} não tem turmas (data vazio)`),[]):t}catch(t){throw console.error(`Erro ao buscar turmas do curso ${e}:`,t),new Error(t.message||"Erro ao buscar curso da oferta")}}async function u1(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(n=>{if(n in e.optional_fields){const a=e.optional_fields[n];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(n)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[n]=a):typeof a=="boolean"?s.optional_fields[n]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[n]=a):a!=null&&a!==""&&(s.optional_fields[n]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol"in s&&delete s.enrol,await Be("local_offermanager_update_class",s)}catch(t){throw new Error(t.message||"Erro ao atualizar turma")}}async function c1(e){try{return await Be("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao excluir turma")}}async function d1(e,t=0,s="",i=[]){try{return await Be("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw new Error(n.message||"Erro ao buscar professores")}}async function f1(){try{return await Be("local_offermanager_get_situation_list",{})}catch(e){throw new Error(e.message||"Erro ao buscar situações de matrícula")}}async function h1(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Be("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw new Error(s.message||"Erro ao duplicar turma")}}async function p1(e){try{const t=parseInt(e,10);if(isNaN(t))throw new Error("ID da turma inválido");const s=await Be("local_offermanager_get_duplication_courses",{offerclassid:t});let i;return s&&s.data&&Array.isArray(s.data)?i=s.data:i=s,Array.isArray(i)?i.map(a=>({id:a.id,name:a.name||a.fullname,courseid:a.courseid||null,offercourseid:a.offercourseid||a.id,categoryid:a.categoryid||null,category_name:a.category_name||""})):[]}catch(t){try{const s=await Be("local_offermanager_get_class",{id:parseInt(e,10)});let i,n;if(s&&s.data)i=s.data.offerid,n=s.data.offercourseid;else if(s)i=s.offerid,n=s.offercourseid;else throw new Error("Não foi possível determinar a oferta da turma");const a=await Be("local_offermanager_get_offer_courses",{offerid:parseInt(i,10)});let u=[];return a&&Array.isArray(a.data)?u=a.data:a&&a.data&&Array.isArray(a.data.courses)?u=a.data.courses:Array.isArray(a)&&(u=a),u.filter(m=>(m.id||m.offercourseid)!=n).map(m=>({id:m.id,name:m.fullname||m.name,courseid:m.courseid||null,offercourseid:m.id,categoryid:m.categoryid||null,category_name:m.category_name||""}))}catch{throw new Error(t.message||"Erro ao buscar cursos para duplicação")}}}async function ta(e){try{return await Be("local_offermanager_get_course_roles",{offercourseid:e})}catch(t){throw new Error(t.message||"Erro ao buscar papéis do curso")}}async function lp(e=!0){try{return await Be("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw new Error(t.message||"Erro ao buscar métodos de inscrição")}}async function m1(e,t){try{return await Be("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw new Error(s.message||"Erro ao alterar status da turma")}}const QR="",g1={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},v1={class:"table-responsive"},_1={class:"table"},y1=["data-value"],b1=["onClick"],w1=["data-column"];function E1(e,t,s,i,n,a){return O(),N("div",v1,[f("table",_1,[f("thead",null,[f("tr",null,[(O(!0),N(Me,null,mt(s.headers,u=>(O(),N("th",{key:u.value,class:pe({"text-right":u.align==="right"}),style:ls(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Vt(e.$slots,"header-select",{key:0},()=>[tt(W(u.text),1)],!0):(O(),N(Me,{key:1},[tt(W(u.text)+" ",1),u.sortable?(O(),N("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[f("i",{class:pe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,b1)):ce("",!0)],64))],14,y1))),128))])]),f("tbody",null,[(O(!0),N(Me,null,mt(s.items,u=>(O(),N("tr",{key:u.id},[(O(!0),N(Me,null,mt(s.headers,c=>(O(),N("td",{key:c.value,class:pe({"text-right":c.align==="right"}),"data-column":c.value},[Vt(e.$slots,"item-"+c.value,{item:u},()=>[tt(W(u[c.value]),1)],!0)],10,w1))),128))]))),128))])])])}const pn=ze(g1,[["render",E1],["__scopeId","data-v-35ce6ca5"]]),ZR="",C1={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},D1={class:"select-wrapper"},x1=["value","disabled"],S1=["value"],O1={key:1,class:"error-message"};function I1(e,t,s,i,n,a){return O(),N("div",{ref:"selectContainer",class:"custom-select-container",style:ls(a.customWidth)},[s.label?(O(),N("div",{key:0,class:pe(["select-label",{disabled:s.disabled}])},W(s.label),3)):ce("",!0),f("div",D1,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:pe(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),N(Me,null,mt(s.options,u=>(O(),N("option",{key:u.value,value:u.value},W(u.label),9,S1))),128))],42,x1),f("div",{class:pe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),N("div",O1,W(s.errorMessage),1)):ce("",!0)],4)}const mr=ze(C1,[["render",I1],["__scopeId","data-v-c65f2fc1"]]),JR="",N1={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},T1={key:0,class:"input-label"},A1=["type","placeholder","value","disabled","min","max"],M1={key:0,class:"search-icon"},P1={key:2,class:"error-message"};function k1(e,t,s,i,n,a){return O(),N("div",{class:"custom-input-container",style:ls(a.customWidth)},[s.label?(O(),N("div",T1,W(s.label),1)):ce("",!0),f("div",{class:pe(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[f("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:pe(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,A1),s.hasSearchIcon?(O(),N("div",M1,t[2]||(t[2]=[f("i",{class:"fas fa-search"},null,-1)]))):ce("",!0),a.isDateType?(O(),N("div",{key:1,class:pe(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[f("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):ce("",!0),s.hasError&&s.errorMessage?(O(),N("div",P1,W(s.errorMessage),1)):ce("",!0)],2)],4)}const Lo=ze(N1,[["render",k1],["__scopeId","data-v-ee21b46c"]]),XR="",R1={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"]},V1=["id","checked","disabled"],F1=["for"];function U1(e,t,s,i,n,a){return O(),N("div",{class:pe(["checkbox-container",{disabled:s.disabled}])},[f("input",{type:"checkbox",id:s.id,checked:s.modelValue,onChange:t[0]||(t[0]=u=>e.$emit("update:modelValue",u.target.checked)),class:"custom-checkbox",disabled:s.disabled},null,40,V1),f("label",{for:s.id,class:pe(["checkbox-label",{disabled:s.disabled}])},[Vt(e.$slots,"default",{},()=>[tt(W(s.label),1)],!0)],10,F1)],2)}const sa=ze(R1,[["render",U1],["__scopeId","data-v-bb633156"]]),eV="",L1={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["click"]},B1=["disabled"],$1={key:1};function j1(e,t,s,i,n,a){return O(),N("button",{class:pe(["custom-button",[`btn-${s.variant}`]]),disabled:s.disabled,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(O(),N("i",{key:0,class:pe(s.icon)},null,2)):ce("",!0),s.label?(O(),N("span",$1,W(s.label),1)):ce("",!0),Vt(e.$slots,"default",{},void 0,!0)],10,B1)}const Bn=ze(L1,[["render",j1],["__scopeId","data-v-9dc7585a"]]),tV="",H1={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},q1={class:"filter-section"},z1={key:0},W1={class:"filter-content"},G1={key:1,class:"filter-tags"};function K1(e,t,s,i,n,a){return O(),N("div",q1,[s.title?(O(),N("h2",z1,W(s.title),1)):ce("",!0),f("div",W1,[Vt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(O(),N("div",G1,[Vt(e.$slots,"tags",{},void 0,!0)])):ce("",!0)])}const up=ze(H1,[["render",K1],["__scopeId","data-v-0a9c42cf"]]),sV="",Y1={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function Q1(e,t,s,i,n,a){return O(),N("div",{class:pe(["filter-row",{"filter-row-inline":s.inline}])},[Vt(e.$slots,"default",{},void 0,!0)],2)}const ra=ze(Y1,[["render",Q1],["__scopeId","data-v-6725a4ba"]]),rV="",Z1={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},J1={key:0,class:"filter-label"},X1={class:"filter-input"};function ew(e,t,s,i,n,a){return O(),N("div",{class:pe(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(O(),N("div",J1,W(s.label),1)):ce("",!0),f("div",X1,[Vt(e.$slots,"default",{},void 0,!0)])],2)}const na=ze(Z1,[["render",ew],["__scopeId","data-v-f69fad7e"]]),nV="",tw={name:"FilterActions"},sw={class:"filter-actions"};function rw(e,t,s,i,n,a){return O(),N("div",sw,[Vt(e.$slots,"default",{},void 0,!0)])}const cp=ze(tw,[["render",rw],["__scopeId","data-v-b9facd34"]]),oV="",nw={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},ow={key:0};function iw(e,t,s,i,n,a){return O(),Pt(Lf,null,{default:ye(()=>[s.isLoading?(O(),N("div",ow,t[0]||(t[0]=[f("div",{class:"modal-overlay"},null,-1),f("div",{class:"loader-wrapper"},[f("span",{class:"loader",role:"status"},[f("span",{class:"sr-only"},"Carregando...")])],-1)]))):ce("",!0)]),_:1})}const vu=ze(nw,[["render",iw],["__scopeId","data-v-a4a23ca1"]]),iV="",aw={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},lw={class:"toast-content"};function uw(e,t,s,i,n,a){return O(),Pt(t_,{to:"body"},[A(Lf,{name:"toast"},{default:ye(()=>[s.show?(O(),N("div",{key:0,class:pe(["toast",s.type])},[f("div",lw,[f("i",{class:pe(a.icon)},null,2),f("span",null,W(s.message),1)]),f("div",{class:"toast-progress",style:ls(a.progressStyle)},null,4)],2)):ce("",!0)]),_:1})])}const Bo=ze(aw,[["render",uw],["__scopeId","data-v-4e0ca8ca"]]),aV="",cw={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,20,50]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},dw={class:"pagination-container mt-3"},fw={class:"pagination-info"},hw=["value"],pw={class:"pagination-text"},mw={class:"pagination-controls"},gw=["disabled"],vw=["onClick"],_w=["disabled"];function yw(e,t,s,i,n,a){return O(),N("div",dw,[f("div",fw,[vt(f("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(O(!0),N(Me,null,mt(s.perPageOptions,u=>(O(),N("option",{key:u,value:u},W(u),9,hw))),128))],544),[[Zl,a.perPageModel]]),f("span",pw," Mostrando de "+W(a.from)+" até "+W(a.to)+" de "+W(s.total)+" resultados ",1)]),f("div",mw,[f("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[f("i",{class:"fas fa-chevron-left"},null,-1)]),8,gw),(O(!0),N(Me,null,mt(a.visiblePages,u=>(O(),N("button",{key:u,class:pe(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},W(u),11,vw))),128)),f("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[f("i",{class:"fas fa-chevron-right"},null,-1)]),8,_w)])])}const mn=ze(cw,[["render",yw],["__scopeId","data-v-cd2746ef"]]),lV="",bw={name:"PageHeader",props:{title:{type:String,required:!0}}},ww={class:"page-header"},Ew={class:"header-actions"};function Cw(e,t,s,i,n,a){return O(),N("div",ww,[f("h2",null,W(s.title),1),f("div",Ew,[Vt(e.$slots,"actions",{},void 0,!0)])])}const oa=ze(bw,[["render",Cw],["__scopeId","data-v-5266bf48"]]),uV="",Dw={name:"Modal",components:{CustomButton:Bn},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},xw={class:"modal-body"},Sw={key:0,class:"modal-footer"},Ow={key:1,class:"modal-footer"};function Iw(e,t,s,i,n,a){const u=X("custom-button");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Ft(()=>{},["stop"]))},[f("div",xw,[Vt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(O(),N("div",Sw,[Vt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(O(),N("div",Ow,[A(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),A(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):ce("",!0)],2)])):ce("",!0)}const Nw=ze(Dw,[["render",Iw],["__scopeId","data-v-87998e77"]]),cV="",Tw={name:"ConfirmationModal",components:{Modal:Nw},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},Aw={key:0,class:"icon-container"},Mw={class:"modal-custom-title"},Pw={key:1,class:"message-list"},kw={key:0,class:"list-title"},Rw={key:2,class:"message"},Vw={class:"modal-custom-footer"},Fw=["disabled"];function Uw(e,t,s,i,n,a){const u=X("modal");return O(),Pt(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:"sm","show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:ye(()=>[f("div",{class:pe(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(O(),N("div",Aw,[f("i",{class:pe(a.iconClass)},null,2)])):ce("",!0),f("h3",Mw,W(s.title),1),a.hasListContent?(O(),N("div",Pw,[s.listTitle?(O(),N("p",kw,W(s.listTitle),1)):ce("",!0),f("ul",null,[(O(!0),N(Me,null,mt(s.listItems,(c,h)=>(O(),N("li",{key:h},W(c),1))),128))])])):(O(),N("div",Rw,W(s.message),1)),f("div",Vw,[f("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},W(s.cancelButtonText),1),f("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},W(s.confirmButtonText),9,Fw)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled"])}const _u=ze(Tw,[["render",Uw],["__scopeId","data-v-3be135e0"]]),dV="",fV="",Lw={name:"OfferManagerView",components:{CustomTable:pn,CustomSelect:mr,CustomInput:Lo,CustomCheckbox:sa,CustomButton:Bn,FilterSection:up,FilterRow:ra,FilterGroup:na,FilterActions:cp,Pagination:mn,PageHeader:oa,ConfirmationModal:_u,LFLoading:vu,Toast:Bo},setup(){return{router:Ji()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:tp},inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],tableHeaders:[{text:"NOME DA OFERTA",value:"name",sortable:!0},{text:"DESCRIÇÃO",value:"description",sortable:!0},{text:"STATUS DA OFERTA",value:"status",sortable:!0},{text:"TIPO DA OFERTA",value:"type",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=sp.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){var e;try{const t=await pu();(e=t==null?void 0:t.data)!=null&&e.types&&(this.typeOptions=t.data.types.map(s=>typeof s=="object"&&s.value&&s.label?s:{value:s,label:s.charAt(0).toUpperCase()+s.slice(1)}))}catch(t){this.error=t.message}},async loadOffers(){try{this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await X0(e),s=Array.isArray(t)?t[0]:t;if(!s.error&&s.data)this.offers=s.data.offers||[],this.totalOffers=s.data.total_items||0;else throw new Error(s.message||"Erro ao carregar ofertas")}catch(e){this.error=e.message}finally{this.loading=!1}},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"nova-oferta"})},editOffer(e){this.router.push({name:"editar-oferta",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await e1(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await o1(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},Bw={id:"offer-manager-component",class:"offer-manager"},$w={class:"new-offer-container"},jw={key:0,class:"alert alert-danger"},Hw={class:"table-container"},qw=["title"],zw={class:"action-buttons"},Ww=["onClick"],Gw=["onClick","disabled","title"],Kw={key:0,class:"fas fa-eye"},Yw={key:1,class:"fas fa-eye-slash"},Qw=["onClick","disabled","title"];function Zw(e,t,s,i,n,a){var we,Q,he,be,Ae,le;const u=X("CustomButton"),c=X("PageHeader"),h=X("CustomInput"),m=X("FilterGroup"),p=X("CustomSelect"),v=X("CustomCheckbox"),w=X("FilterActions"),D=X("FilterRow"),k=X("FilterSection"),U=X("CustomTable"),te=X("Pagination"),T=X("ConfirmationModal"),se=X("LFLoading"),G=X("Toast");return O(),N("div",Bw,[A(c,{title:"Gerenciar Ofertas"},{actions:ye(()=>[f("div",$w,[A(u,{variant:"primary",label:"Nova Oferta",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),A(k,{title:"FILTRO"},{default:ye(()=>[A(D,{inline:!0},{default:ye(()=>[A(m,{label:"Oferta"},{default:ye(()=>[A(h,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=ie=>n.inputFilters.search=ie),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),A(m,{label:"Tipo"},{default:ye(()=>[A(p,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=ie=>n.inputFilters.type=ie),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),A(m,{"is-checkbox":!0},{default:ye(()=>[A(v,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=ie=>n.inputFilters.hideInactive=ie),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),A(w,null,{default:ye(()=>[A(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(O(),N("div",jw,[t[7]||(t[7]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),tt(" "+W(n.error),1)])):ce("",!0),f("div",Hw,[A(U,{headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":ye(({item:ie})=>[f("span",{title:ie.description},W(ie.description.length>50?ie.description.slice(0,50)+"...":ie.description),9,qw)]),"item-type":ye(({item:ie})=>[tt(W(ie.type.charAt(0).toUpperCase()+ie.type.slice(1)),1)]),"item-status":ye(({item:ie})=>[tt(W(ie.status===1?"Ativa":"Inativa"),1)]),"item-actions":ye(({item:ie})=>[f("div",zw,[f("button",{class:"btn-action btn-edit",onClick:ke=>a.editOffer(ie),title:"Editar"},t[8]||(t[8]=[f("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_9_197955)"},[f("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--primary)"})]),f("defs",null,[f("clipPath",{id:"clip0_9_197955"},[f("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,Ww),f("button",{class:pe(["btn-action",ie.status===1?"btn-deactivate":"btn-activate"]),onClick:ke=>a.toggleOfferStatus(ie),disabled:ie.status===0&&!ie.can_activate,title:a.getStatusButtonTitle(ie)},[ie.status===1?(O(),N("i",Kw)):(O(),N("i",Yw))],10,Gw),f("button",{class:"btn-action btn-delete",onClick:ke=>a.deleteOffer(ie),disabled:!ie.can_delete,title:ie.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[9]||(t[9]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,Qw)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),A(te,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=ie=>n.currentPage=ie),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=ie=>n.perPage=ie),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),A(T,{show:n.showDeleteModal,title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=ie=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),A(T,{show:n.showStatusModal,title:((we=n.selectedOffer)==null?void 0:we.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((Q=n.selectedOffer)==null?void 0:Q.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((he=n.selectedOffer)==null?void 0:he.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((be=n.selectedOffer)==null?void 0:be.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ae=n.selectedOffer)==null?void 0:Ae.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((le=n.selectedOffer)==null?void 0:le.status)===1?"warning":"question",onClose:t[6]||(t[6]=ie=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),A(se,{"is-loading":n.loading},null,8,["is-loading"]),A(G,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Jw=ze(Lw,[["render",Zw],["__scopeId","data-v-49ab01d8"]]);async function yu(e={}){try{const t=await Be("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"});return t?t.error?(console.error("Erro na resposta de fetchEnrolments:",t.error),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}}):Array.isArray(t)?t.length>0?{data:t[0]}:(console.error("Array de resposta vazio"),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}}):{data:t}:(console.error("Resposta vazia de fetchEnrolments"),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}})}catch(t){return console.error("Exceção em fetchEnrolments:",t),{data:{page:e.page||1,perpage:e.perpage||20,total:0,enrolments:[]}}}}async function Xw(e={}){try{const t=await Be("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5});return t?Array.isArray(t)||t&&Array.isArray(t.data)||t&&t.error===!1&&Array.isArray(t.data)?t:(console.error("Formato de resposta não reconhecido:",t),{error:!0,data:[],message:"Formato de resposta não reconhecido"}):(console.error("Resposta vazia de enrolUsers"),{error:!0,data:[]})}catch(t){return console.error("Erro ao matricular usuários:",t),{error:!0,data:[],message:t.message||"Erro ao matricular usuários"}}}async function bu(e={}){try{const t=await yu({offerclassid:e.offerclassid,userids:[],page:1,perpage:100});if(!t||!t.data)return console.error("Resposta vazia de getFilterOptions"),[];const s=t.data.data||t.data;return!s.enrolments||!Array.isArray(s.enrolments)?(console.error("Resposta não contém enrolments ou não é um array:",s),[]):s.enrolments.map(n=>{const a={id:n.userid};switch(e.filter_type){case"name":a.fullname=n.fullname;break;case"email":a.email=n.email;break;case"cpf":a.cpf=n.cpf;break}return a})}catch(t){return console.error("Erro ao buscar opções de filtro:",t),[]}}async function eE(e){try{const t=await Be("local_offermanager_get_potential_users_to_enrol",{offerclassid:e});return t?t.error?(console.error("Erro na resposta de getPotentialUsersToEnrol:",t.error),[]):Array.isArray(t)?t:t.data&&Array.isArray(t.data)?t.data:(console.warn("Resposta não é um array nem contém um array em .data:",t),[]):(console.error("Resposta vazia de getPotentialUsersToEnrol"),[])}catch(t){return console.error("Erro ao buscar usuários potenciais:",t),[]}}async function tE(e={}){try{const t=await Be("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid});return t==null?(console.error("Resposta vazia de editEnrolment"),!1):typeof t=="boolean"?t:t&&typeof t.success=="boolean"?t.success:t&&t.error===!1?t.data===!0:(console.warn("Formato de resposta não reconhecido:",t),!1)}catch(t){return console.error("Erro ao editar matrícula:",t),!1}}async function sE(e={}){try{const t=await Be("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend});return t&&t.error===!1&&t.data===!0?e.offeruserenrolids.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.error("Formato de resposta não reconhecido:",t),[])}catch(t){throw console.error("Erro ao editar matrículas em lote:",t),t}}async function rE(e){try{const t=await Be("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e});return t&&t.error===!1&&t.data===!0?e.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Formato de resposta não reconhecido:",t),[])}catch(t){return console.error("Erro ao excluir matrículas em lote:",t),[]}}async function nE(e){try{const t=await Be("local_offermanager_get_roles",{offeruserenrolid:e});return t?t.error?(console.error("Erro na resposta de getUserRoles:",t.error),[]):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:[]:(console.error("Resposta vazia de getUserRoles"),[])}catch(t){return console.error("Erro ao buscar papéis do usuário:",t),[]}}async function oE(e,t){try{const s=await Be("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]});return s==null?(console.error("Resposta vazia de updateUserRoles"),!1):typeof s=="boolean"?s:s&&typeof s.success=="boolean"?s.success:s&&s.error===!1?s.data===!0:(console.warn("Formato de resposta não reconhecido:",s),!1)}catch(s){return console.error("Erro ao atualizar papéis do usuário:",s),!1}}const hV="",iE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},aE={class:"select-wrapper"},lE=["value","disabled"],uE=["label"],cE=["value"],dE={key:1,class:"error-message"};function fE(e,t,s,i,n,a){return O(),N("div",{ref:"selectContainer",class:"hierarchical-select-container",style:ls(a.customWidth)},[s.label?(O(),N("div",{key:0,class:pe(["select-label",{disabled:s.disabled}])},W(s.label),3)):ce("",!0),f("div",aE,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:pe(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),N(Me,null,mt(s.options,u=>(O(),N("optgroup",{key:u.value,label:u.label},[(O(!0),N(Me,null,mt(u.children,c=>(O(),N("option",{key:c.value,value:c.value,class:"child-option"},W(c.label),9,cE))),128))],8,uE))),128))],42,lE),f("div",{class:pe(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),N("div",dE,W(s.errorMessage),1)):ce("",!0)],4)}const hE=ze(iE,[["render",fE],["__scopeId","data-v-ca8af705"]]),pV="",pE={name:"FilterTag",emits:["remove"]};function mE(e,t,s,i,n,a){return O(),N("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=f("i",{class:"fas fa-times"},null,-1)),Vt(e.$slots,"default",{},void 0,!0)])}const $o=ze(pE,[["render",mE],["__scopeId","data-v-cf6f2168"]]),mV="",gE={name:"FilterTags"},vE={class:"filter-tags"};function _E(e,t,s,i,n,a){return O(),N("div",vE,[Vt(e.$slots,"default",{},void 0,!0)])}const ia=ze(gE,[["render",_E],["__scopeId","data-v-746bf68d"]]),gV="",yE={name:"Autocomplete",components:{FilterTag:$o,FilterTags:ia},props:{modelValue:{type:[Array,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){if(!this.modelValue)return"";const e=this.internalItems.find(t=>t.value===this.modelValue);return e?e.label:""}},created(){this.debouncedSearch=sp.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){Array.isArray(this.modelValue)&&this.$emit("select-all"),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e.value),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},bE={class:"autocomplete-container"},wE=["id"],EE={class:"autocomplete-wrapper"},CE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],DE={key:0,class:"selected-item"},xE=["title"],SE=["id"],OE=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],IE={class:"item-label"},NE={key:0,class:"fas fa-check"},TE={key:0,class:"dropdown-item loading-item"},AE={key:1,class:"dropdown-item no-results"},ME={key:0,class:"tags-container"};function PE(e,t,s,i,n,a){const u=X("FilterTag"),c=X("FilterTags");return O(),N("div",bE,[s.label?(O(),N("label",{key:0,class:pe(["filter-label",{required:s.required}]),id:`${n.uniqueId}-label`},W(s.label),11,wE)):ce("",!0),f("div",EE,[f("div",{class:"input-container",style:ls({maxWidth:a.inputMaxWidthStyle})},[f("div",{class:pe(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[vt(f("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>n.searchQuery=h),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,CE),[[ws,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(O(),N("div",DE,[f("span",{class:"selected-text",title:a.getSelectedItemLabel},W(a.truncateLabel(a.getSelectedItemLabel)),9,xE),f("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Ft((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):ce("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(O(),N("i",{key:1,class:pe(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):ce("",!0)],2),n.isOpen?(O(),N("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(O(),N(Me,{key:0},[(O(!0),N(Me,null,mt(a.displayItems,(h,m)=>(O(),N("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:pe(["dropdown-item",{active:n.selectedIndex===m,selected:h.value!=="__ALL__"&&(Array.isArray(s.modelValue)?s.modelValue.some(p=>p.value===h.value):s.modelValue===h.value)}]),id:`${n.uniqueId}-option-${m}`,role:"option","data-index":m,"aria-selected":n.selectedIndex===m,tabindex:n.selectedIndex===m?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,m),ref_for:!0,ref:"optionElements",title:h.label},[f("span",IE,W(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(s.modelValue)&&s.modelValue.some(p=>p.value===h.value)?(O(),N("i",NE)):ce("",!0)],42,OE))),128)),s.loading?(O(),N("div",TE,t[8]||(t[8]=[f("span",null,"Carregando mais itens...",-1)]))):ce("",!0)],64)):(O(),N("div",AE,W(s.noResultsText||"Nenhum item disponível"),1))],40,SE)):ce("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(O(),N("div",ME,[A(c,null,{default:ye(()=>[(O(!0),N(Me,null,mt(s.modelValue,h=>(O(),Pt(u,{key:h.value,onRemove:m=>a.removeItem(h)},{default:ye(()=>[tt(W(h.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):ce("",!0)])])}const jo=ze(yE,[["render",PE],["__scopeId","data-v-e7ee87a5"]]),vV="",kE={name:"EnrolmentModalNew",components:{Toast:Bo,CustomSelect:mr},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:[Number,String],required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",roleOptions:[],searchQuery:"",isOpen:!1,selectedIndex:-1,userOptions:[],selectedUsers:[],selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingRoles:!0,loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},computed:{isFormValid(){return this.roleOptions.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1},filteredUsers(){if(!this.searchQuery)return this.userOptions;const e=this.searchQuery.toLowerCase();return this.userOptions.filter(t=>t.label.toLowerCase().includes(e))}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=this.$el.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1,this.selectedIndex=-1)},async initializeForm(){if(this.offerclassid)try{await this.loadRoles(),await this.loadUsers(),this.resetForm()}catch(e){console.error("Erro ao inicializar formulário:",e)}},resetForm(){if(this.enrolmentMethod="manual",this.roleOptions.length>0){const e=this.roleOptions.find(t=>t.label.toLowerCase().includes("aluno")||t.label.toLowerCase().includes("estudante")||t.label.toLowerCase().includes("student"));this.selectedRoleId=e?e.value:this.roleOptions[0].value}else this.selectedRoleId="";this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8"},async loadRoles(){this.loadingRoles=!0;try{if(!this.offerclassid)return;const e=await Lr(parseInt(this.offerclassid));let t;if(e&&e.data?t=e.data.offercourseid:e&&(t=e.offercourseid),!t)throw new Error("Offercourseid não encontrado");const s=await ta(t);let i=[];if(s&&Array.isArray(s)?i=s:s&&s.data&&Array.isArray(s.data)&&(i=s.data),i.length>0){this.roleOptions=i.map(a=>({value:String(a.id),label:a.name}));const n=this.roleOptions.find(a=>a.value==="5");n?this.selectedRoleId=n.value:this.roleOptions.length>0&&(this.selectedRoleId=this.roleOptions[0].value)}else console.error("Nenhum papel disponível para este curso")}catch(e){console.error("Erro ao carregar papéis:",e),this.roleOptions=[],this.selectedRoleId=""}finally{this.loadingRoles=!1}},async loadUsers(){try{if(!this.offerclassid){console.error("EnrolmentModalNew - loadUsers: ID da turma não definido"),this.userOptions=[];return}this.loadingUsers=!0;const e=parseInt(this.offerclassid);if(isNaN(e)){this.userOptions=[];return}const t=await eE(e),i=(await this.getEnrolledUsers(e)).map(n=>n.id);if(t&&Array.isArray(t)){const n=t.filter(a=>!i.includes(a.id));this.userOptions=n.map(a=>({value:a.id,label:a.fullname}))}else this.userOptions=[]}catch(e){console.error("Erro ao carregar usuários:",e),this.userOptions=[]}finally{this.loadingUsers=!1}},async getEnrolledUsers(e){try{const t=await yu({offerclassid:e,page:1,perpage:1e3});if(console.log("EnrolmentModalNew - Resposta de fetchEnrolments:",t),t&&t.data){const s=t.data.data||t.data;if(s&&Array.isArray(s.enrolments))return s.enrolments.map(i=>({id:i.userid}))}return[]}catch(t){return console.error("Erro ao obter usuários matriculados:",t),[]}},handleFocus(){this.isOpen=!0,this.selectedIndex=-1},handleInput(){this.isOpen=!0},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(m,p)=>{if(p==="\\t")return m.split("	");if(p===" ")return m.split(/\s+/);{const v=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return m.split(new RegExp(v))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(m=>m.includes("userid"))||!u.some(m=>m.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(m=>m.includes("userid")),h=u.findIndex(m=>m.includes("firstname"));for(let m=1;m<i.length;m++){const p=i[m].trim();if(!p)continue;const v=a(p,t);if(v.length>Math.max(c,h)){const w=v[c].trim(),D=v[h].trim();if(w&&D){if(!/^\d+$/.test(w)){console.warn(`Linha ${m+1}: ID inválido '${w}'. Deve ser um número.`);continue}n.push({id:w,name:D})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(a=>a.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(a=>parseInt(a.id)));const t=await Xw({offerclassid:parseInt(this.offerclassid),userids:e,roleid:parseInt(this.selectedRoleId)});console.log("Resposta da matrícula:",JSON.stringify(t,null,2));let s=[],i=!1;if(Array.isArray(t))s=t;else if(t&&Array.isArray(t.data))s=t.data;else if(Array.isArray(t)&&t.length>0&&t[0].data)s=t[0].data;else if(Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data))s=t[0].data;else{console.error("Formato de resposta não reconhecido:",t),this.showErrorMessage("Erro ao processar a resposta do servidor. Verifique o console para mais detalhes.");return}const n=s.filter(a=>a.success).length;if(i=n>0,i)this.showSuccessMessage(`${n} de ${e.length} usuário(s) matriculado(s) com sucesso.`),setTimeout(()=>{this.$emit("success",{count:n,total:e.length}),this.$emit("close")},1500);else{console.error("Nenhum usuário foi matriculado com sucesso");let a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.";s.length>0&&s[0].message&&(s[0].message.includes("já está matriculado")||s[0].message.includes("already enrolled")?a=s[0].message:s[0].message.includes("Error enrolling user")||s[0].message.includes("[[message:")||s[0].message.includes("enrolment_failed")?a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.":a=s[0].message),this.showErrorMessage(a)}}catch(e){console.error("Erro ao matricular usuários:",e),this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},RE={class:"modal-header"},VE={class:"modal-title"},FE={class:"modal-body"},UE={class:"enrolment-modal"},LE={class:"form-row"},BE={class:"form-group"},$E={class:"limited-width-input"},jE={class:"form-group"},HE={class:"limited-width-input"},qE={key:0,class:"error-message"},zE={key:0,class:"form-group"},WE={class:"user-select-container"},GE={class:"custom-autocomplete-wrapper"},KE={key:0,class:"dropdown-menu show"},YE=["onClick"],QE={key:0,class:"fas fa-check"},ZE={key:0,class:"selected-users-container"},JE={class:"filter-tags"},XE=["onClick"],eC={key:1,class:"form-group"},tC={class:"file-name"},sC={class:"file-size"},rC={key:0,class:"csv-users-preview"},nC={class:"preview-header"},oC={class:"selected-users-container"},iC={class:"filter-tags"},aC={key:0,class:"more-users"},lC={class:"csv-info"},uC={class:"csv-example"},cC=["href"],dC={class:"csv-options-row"},fC={class:"csv-option"},hC={class:"csv-option"},pC={class:"modal-footer"},mC=["disabled"];function gC(e,t,s,i,n,a){const u=X("CustomSelect"),c=X("Toast");return O(),N(Me,null,[s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[16]||(t[16]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[15]||(t[15]=Ft(()=>{},["stop"]))},[f("div",RE,[f("h3",VE,W(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[17]||(t[17]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",FE,[f("div",UE,[t[32]||(t[32]=f("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),f("div",LE,[f("div",BE,[t[18]||(t[18]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Forma de matrícula"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",$E,[A(u,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>n.enrolmentMethod=h),options:n.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),f("div",jE,[t[19]||(t[19]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Papel para atribuir"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",HE,[A(u,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedRoleId=h),options:n.roleOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"]),!n.loadingRoles&&n.roleOptions.length===0?(O(),N("div",qE," Não foi possível carregar os papéis disponíveis para esta turma. ")):ce("",!0)])])]),n.enrolmentMethod==="manual"?(O(),N("div",zE,[t[22]||(t[22]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Selecionar usuários"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",WE,[f("div",GE,[vt(f("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>n.searchQuery=h),onFocus:t[4]||(t[4]=(...h)=>a.handleFocus&&a.handleFocus(...h)),onInput:t[5]||(t[5]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[ws,n.searchQuery]]),t[20]||(t[20]=f("div",{class:"select-arrow"},null,-1)),n.isOpen?(O(),N("div",KE,[(O(!0),N(Me,null,mt(a.filteredUsers,(h,m)=>(O(),N("div",{key:h.value,class:pe(["dropdown-item",{active:n.selectedIndex===m,selected:n.selectedUsers.some(p=>p.value===h.value)}]),onClick:p=>a.selectUser(h)},[tt(W(h.label)+" ",1),n.selectedUsers.some(p=>p.value===h.value)?(O(),N("i",QE)):ce("",!0)],10,YE))),128))])):ce("",!0)])]),n.selectedUsers.length>0?(O(),N("div",ZE,[f("div",JE,[(O(!0),N(Me,null,mt(n.selectedUsers,h=>(O(),N("div",{key:h.value,class:"tag badge badge-primary",onClick:m=>a.removeUser(h)},[t[21]||(t[21]=f("i",{class:"fas fa-times"},null,-1)),tt(" "+W(h.label),1)],8,XE))),128))])])):ce("",!0)])):ce("",!0),n.enrolmentMethod==="batch"?(O(),N("div",eC,[t[31]||(t[31]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",{class:pe(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[7]||(t[7]=Ft((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[8]||(t[8]=Ft((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[9]||(t[9]=Ft((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[10]||(t[10]=h=>e.$refs.fileInput.click())},[f("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[6]||(t[6]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),n.selectedFile?(O(),N(Me,{key:1},[t[25]||(t[25]=f("div",{class:"file-icon"},[f("i",{class:"fas fa-file-alt"})],-1)),f("p",tC,W(n.selectedFile.name),1),f("p",sC," ("+W(a.formatFileSize(n.selectedFile.size))+") ",1),t[26]||(t[26]=f("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(O(),N(Me,{key:0},[t[23]||(t[23]=f("div",{class:"upload-icon"},[f("i",{class:"fas fa-arrow-down"})],-1)),t[24]||(t[24]=f("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(O(),N("div",rC,[f("div",nC,[f("span",null,"Usuários encontrados no arquivo ("+W(n.csvUsers.length)+"):",1)]),f("div",oC,[f("div",iC,[(O(!0),N(Me,null,mt(n.csvUsers.slice(0,5),h=>(O(),N("div",{key:h.id,class:"tag badge badge-primary"},W(h.name),1))),128)),n.csvUsers.length>5?(O(),N("span",aC,"+"+W(n.csvUsers.length-5)+" mais",1)):ce("",!0)])])])):ce("",!0),f("div",lC,[t[30]||(t[30]=f("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),f("div",uC,[t[27]||(t[27]=f("span",{class:"example-label"},"Exemplo CSV",-1)),f("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,cC)]),f("div",dC,[f("div",fC,[t[28]||(t[28]=f("label",null,"Delimitador do CSV",-1)),A(u,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[11]||(t[11]=h=>n.csvDelimiter=h),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),f("div",hC,[t[29]||(t[29]=f("label",null,"Codificação",-1)),A(u,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[12]||(t[12]=h=>n.csvEncoding=h),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):ce("",!0),t[33]||(t[33]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))])]),f("div",pC,[f("button",{class:"btn btn-primary",onClick:t[13]||(t[13]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:n.isSubmitting||!a.isFormValid},W(s.confirmButtonText),9,mC),f("button",{class:"btn btn-secondary",onClick:t[14]||(t[14]=h=>e.$emit("close"))},W(s.cancelButtonText),1)])],2)])):ce("",!0),A(c,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],64)}const vC=ze(kE,[["render",gC],["__scopeId","data-v-bc87a76b"]]),_V="",_C={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},yC={class:"modal-header"},bC={key:0,class:"modal-body"},wC={class:"details-container"},EC={class:"detail-row"},CC={class:"detail-value"},DC={class:"detail-row"},xC={class:"detail-value"},SC={class:"detail-row"},OC={class:"detail-value"},IC={class:"detail-row"},NC={class:"detail-value"},TC={class:"detail-row"},AC={class:"detail-value"},MC={key:1,class:"modal-body no-data"},PC={class:"modal-footer"};function kC(e,t,s,i,n,a){return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[2]||(t[2]=Ft(()=>{},["stop"]))},[f("div",yC,[t[5]||(t[5]=f("h3",{class:"modal-title"},"Informações da matrícula",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[f("i",{class:"fas fa-times"},null,-1)]))]),s.user?(O(),N("div",bC,[f("div",wC,[f("div",EC,[t[6]||(t[6]=f("div",{class:"detail-label"},"Nome completo",-1)),f("div",CC,W(s.user.fullName),1)]),f("div",DC,[t[7]||(t[7]=f("div",{class:"detail-label"},"Curso",-1)),f("div",xC,W(s.courseName),1)]),f("div",SC,[t[8]||(t[8]=f("div",{class:"detail-label"},"Método de inscrição",-1)),f("div",OC,W(a.getEnrolmentMethod(s.user.enrol)),1)]),f("div",IC,[t[9]||(t[9]=f("div",{class:"detail-label"},"Estado",-1)),f("div",NC,[f("span",{class:pe(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},W(s.user.statusName),3)])]),f("div",TC,[t[10]||(t[10]=f("div",{class:"detail-label"},"Matrícula criada",-1)),f("div",AC,W(s.user.createdDate),1)])])])):(O(),N("div",MC,"Nenhum dado disponível")),f("div",PC,[f("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):ce("",!0)}const RC=ze(_C,[["render",kC],["__scopeId","data-v-ffabdbe2"]]),yV="",VC={name:"EditEnrollmentModal",components:{CustomSelect:mr},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await tE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},FC={class:"modal-header"},UC={class:"modal-title"},LC={class:"modal-body"},BC={class:"enrollment-form"},$C={class:"form-row"},jC={class:"form-value"},HC={class:"form-row"},qC={class:"form-field"},zC={class:"select-wrapper"},WC={class:"form-row"},GC={class:"form-field date-time-field"},KC={class:"date-field"},YC={class:"time-field"},QC={class:"enable-checkbox"},ZC={class:"form-row"},JC={class:"form-field"},XC={class:"select-wrapper"},eD={class:"form-row"},tD={class:"date-field"},sD=["disabled"],rD={class:"time-field"},nD=["disabled"],oD={class:"enable-checkbox"},iD={class:"form-row"},aD={class:"form-value"},lD={class:"modal-footer"},uD={class:"footer-buttons"},cD=["disabled"];function dD(e,t,s,i,n,a){const u=X("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[14]||(t[14]=Ft(()=>{},["stop"]))},[f("div",FC,[f("h3",UC," Editar matrícula de "+W(s.user?s.user.fullName:""),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",LC,[f("div",BC,[f("div",$C,[t[17]||(t[17]=f("div",{class:"form-label"},"Método de inscrição",-1)),f("div",jC,W(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),f("div",HC,[t[18]||(t[18]=f("div",{class:"form-label"},"Estado",-1)),f("div",qC,[f("div",zC,[A(u,{modelValue:n.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>n.formData.status=c),options:n.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",WC,[t[20]||(t[20]=f("div",{class:"form-label"},"Matrícula começa",-1)),f("div",GC,[f("div",KC,[vt(f("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>n.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[ws,n.formData.startDateStr]])]),f("div",YC,[vt(f("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>n.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[ws,n.formData.startTimeStr]])]),f("div",QC,[vt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>n.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[ji,n.formData.enableStartDate]]),t[19]||(t[19]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",ZC,[t[21]||(t[21]=f("div",{class:"form-label"},"Período de validade da matrícula",-1)),f("div",JC,[f("div",XC,[A(u,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>n.formData.validityPeriod=c),options:n.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),f("div",eD,[t[23]||(t[23]=f("div",{class:"form-label"},"Matrícula termina",-1)),f("div",{class:pe(["form-field date-time-field",{"disabled-inputs-only":!n.formData.enableEndDate}])},[f("div",tD,[vt(f("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>n.formData.endDateStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,sD),[[ws,n.formData.endDateStr]])]),f("div",rD,[vt(f("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>n.formData.endTimeStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,nD),[[ws,n.formData.endTimeStr]])]),f("div",oD,[vt(f("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>n.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[ji,n.formData.enableEndDate]]),t[22]||(t[22]=f("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),f("div",iD,[t[24]||(t[24]=f("div",{class:"form-label"},"Matrícula criada",-1)),f("div",aD,W(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),f("div",lD,[t[25]||(t[25]=f("div",{class:"footer-spacer"},null,-1)),f("div",uD,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:n.isSubmitting},W(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,cD),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):ce("",!0)}const fD=ze(VC,[["render",dD],["__scopeId","data-v-f9509e2b"]]),bV="",wV="",hD={name:"BulkEditEnrollmentModal",components:{Pagination:mn,CustomTable:pn,CustomSelect:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,m]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);t=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;t+=v}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,m]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);s=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;s+=v}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await sE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(h=>h.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},pD={class:"modal-header"},mD={class:"modal-body"},gD={class:"enrollment-form"},vD={class:"table-container"},_D={class:"form-row"},yD={class:"form-field"},bD={class:"select-wrapper"},wD={class:"form-row"},ED={class:"form-field date-time-field"},CD={class:"date-field"},DD=["disabled"],xD={class:"time-field"},SD=["disabled"],OD={class:"enable-checkbox"},ID={class:"form-row"},ND={class:"form-field date-time-field"},TD={class:"date-field"},AD=["disabled"],MD={class:"time-field"},PD=["disabled"],kD={class:"enable-checkbox"},RD={class:"modal-footer"},VD={class:"footer-buttons"},FD=["disabled"];function UD(e,t,s,i,n,a){const u=X("CustomTable"),c=X("Pagination"),h=X("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=m=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[16]||(t[16]=Ft(()=>{},["stop"]))},[f("div",pD,[t[19]||(t[19]=f("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=m=>e.$emit("close"))},t[18]||(t[18]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",mD,[f("div",gD,[f("div",null,[f("div",vD,[A(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?vt((O(),Pt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=m=>n.currentPage=m),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=m=>n.perPage=m),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>n.perPage]]):ce("",!0),t[20]||(t[20]=f("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),f("div",_D,[t[21]||(t[21]=f("div",{class:"form-label"},"Alterar o status",-1)),f("div",yD,[f("div",bD,[A(h,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=m=>n.formData.status=m),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",wD,[t[23]||(t[23]=f("div",{class:"form-label"},"Alterar data de início",-1)),f("div",ED,[f("div",CD,[vt(f("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=m=>n.formData.startDateStr=m),class:"form-control",onChange:t[5]||(t[5]=(...m)=>a.handleStartDateChange&&a.handleStartDateChange(...m)),disabled:!n.formData.enableStartDate},null,40,DD),[[ws,n.formData.startDateStr]])]),f("div",xD,[vt(f("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=m=>n.formData.startTimeStr=m),class:"form-control",onChange:t[7]||(t[7]=(...m)=>a.handleStartTimeChange&&a.handleStartTimeChange(...m)),disabled:!n.formData.enableStartDate},null,40,SD),[[ws,n.formData.startTimeStr]])]),f("div",OD,[vt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=m=>n.formData.enableStartDate=m),class:"custom-checkbox"},null,512),[[ji,n.formData.enableStartDate]]),t[22]||(t[22]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",ID,[t[25]||(t[25]=f("div",{class:"form-label"},"Alterar data de fim",-1)),f("div",ND,[f("div",TD,[vt(f("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=m=>n.formData.endDateStr=m),class:"form-control",onChange:t[10]||(t[10]=(...m)=>a.handleEndDateChange&&a.handleEndDateChange(...m)),disabled:!n.formData.enableEndDate},null,40,AD),[[ws,n.formData.endDateStr]])]),f("div",MD,[vt(f("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=m=>n.formData.endTimeStr=m),class:"form-control",onChange:t[12]||(t[12]=(...m)=>a.handleEndTimeChange&&a.handleEndTimeChange(...m)),disabled:!n.formData.enableEndDate},null,40,PD),[[ws,n.formData.endTimeStr]])]),f("div",kD,[vt(f("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=m=>n.formData.enableEndDate=m),class:"custom-checkbox"},null,512),[[ji,n.formData.enableEndDate]]),t[24]||(t[24]=f("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),f("div",RD,[t[26]||(t[26]=f("div",{class:"footer-spacer"},null,-1)),f("div",VD,[f("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...m)=>a.saveChanges&&a.saveChanges(...m)),disabled:n.isSubmitting},W(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,FD),f("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=m=>e.$emit("close"))}," Cancelar ")])])])])):ce("",!0)}const LD=ze(hD,[["render",UD],["__scopeId","data-v-1ade848f"]]),EV="",BD={name:"BulkDeleteEnrollmentModal",components:{Pagination:mn,CustomSelect:mr,CustomTable:pn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},$D={class:"modal-header"},jD={class:"modal-body"},HD={class:"enrollment-form"},qD={class:"table-container"},zD={class:"modal-footer"},WD={class:"footer-buttons"},GD=["disabled"];function KD(e,t,s,i,n,a){const u=X("CustomTable"),c=X("Pagination");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[5]||(t[5]=Ft(()=>{},["stop"]))},[f("div",$D,[t[8]||(t[8]=f("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",jD,[f("div",HD,[f("div",qD,[A(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?vt((O(),Pt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>n.currentPage=h),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>n.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[Kl,s.users.length>n.perPage]]):ce("",!0)]),t[9]||(t[9]=f("div",{class:"text-center mt-5"},[f("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),f("div",zD,[f("div",WD,[f("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:n.isSubmitting},W(n.isSubmitting?"Removendo...":"Remover matrículas"),9,GD),f("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):ce("",!0)}const YD=ze(BD,[["render",KD],["__scopeId","data-v-cd4191df"]]),CV="",QD={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function ZD(e,t,s,i,n,a){return O(),N("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=f("i",{class:"fas fa-angle-left"},null,-1)),tt(" "+W(s.label),1)])}const wu=ze(QD,[["render",ZD],["__scopeId","data-v-eb293b5c"]]),DV="",JD={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},XD=["src"];function ex(e,t,s,i,n,a){return O(),N("div",{class:"user-avatar",style:ls(a.avatarStyle)},[a.hasImage?(O(),N("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,XD)):(O(),N("div",{key:1,class:"avatar-initials",style:ls({backgroundColor:a.backgroundColor})},W(a.initials),5))],4)}const tx=ze(JD,[["render",ex],["__scopeId","data-v-0a49f249"]]),xV="",sx={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await Lr(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await ta(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const n=await nE(this.offeruserenrolid);if(Array.isArray(n)&&n.length)this.selectedRoles=n.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await oE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},rx={class:"role-selector"},nx={key:1,class:"role-edit-wrapper"},ox={class:"role-edit-container"},ix={class:"select-wrapper"},ax=["value"],lx={class:"role-actions"},ux={key:2,class:"loading-overlay"};function cx(e,t,s,i,n,a){return O(),N("div",rx,[n.isEditing?(O(),N("div",nx,[f("div",ox,[f("div",ix,[vt(f("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>n.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Ft(()=>{},["stop"])),style:ls({height:Math.max(4,n.roles.length)*25+"px"})},[(O(!0),N(Me,null,mt(n.roles,u=>(O(),N("option",{key:u.id,value:u.id},W(u.name),9,ax))),128))],4),[[Zl,n.selectedRoles]])]),f("div",lx,[f("button",{class:"btn-save",onClick:t[3]||(t[3]=Ft((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[f("i",{class:"fas fa-check"},null,-1)])),f("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Ft((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))])])])):(O(),N("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Ft((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[f("span",null,W(a.displayRoleNames),1),t[5]||(t[5]=f("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),n.loading&&n.isEditing?(O(),N("div",ux,t[8]||(t[8]=[f("div",{class:"spinner"},null,-1)]))):ce("",!0)])}const dx=ze(sx,[["render",cx],["__scopeId","data-v-21b55063"]]),SV="",fx={name:"RegisteredUsers",props:{offerclassid:{type:String,required:!0}},components:{CustomTable:pn,CustomSelect:mr,HierarchicalSelect:hE,CustomInput:Lo,CustomCheckbox:sa,CustomButton:Bn,FilterSection:up,FilterRow:ra,FilterGroup:na,FilterActions:cp,FilterTag:$o,FilterTags:ia,Pagination:mn,PageHeader:oa,ConfirmationModal:_u,Autocomplete:jo,EnrolmentModalNew:vC,EnrollmentDetailsModal:RC,Toast:Bo,EditEnrollmentModal:fD,BulkEditEnrollmentModal:LD,BulkDeleteEnrollmentModal:YD,BackButton:wu,UserAvatar:tx,RoleSelector:dx,LFLoading:vu},setup(){return{router:Ji()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:tp},offerId:null,inputFilters:{name:[],cpf:[],email:[]},appliedFilters:{name:[],cpf:[],email:[]},nameOptions:[],cpfOptions:[],emailOptions:[],selectedName:"",selectedCpf:"",selectedEmail:"",selectedUserIds:{name:[],cpf:[],email:[]},tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectAll:!1,selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},computed:{hasActiveFilters(){return this.appliedFilters.name&&this.appliedFilters.name.length>0||this.appliedFilters.cpf&&this.appliedFilters.cpf.length>0||this.appliedFilters.email&&this.appliedFilters.email.length>0},allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},filteredNameOptions(){const e=this.appliedFilters.name.map(t=>t.value);return this.nameOptions.filter(t=>!e.includes(t.value))},filteredCpfOptions(){const e=this.appliedFilters.cpf.map(t=>t.value);return this.cpfOptions.filter(t=>!e.includes(t.value))},filteredEmailOptions(){const e=this.appliedFilters.email.map(t=>t.value);return this.emailOptions.filter(t=>!e.includes(t.value))}},watch:{perPage(e,t){if(e!==t){this.currentPage=1,this.selectedUsers=[];const s=this.offerclassid||this.$route.params.offerclassid;s&&this.loadRegisteredUsers(s)}},currentPage(e,t){if(e!==t){this.selectedUsers=[];const s=this.offerclassid||this.$route.params.offerclassid;s&&this.loadRegisteredUsers(s)}}},async created(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(e){try{const t=await Lr(parseInt(e));if(this.classDetails=(t==null?void 0:t.data)||{},t&&t.error===!1&&Array.isArray(t.data)){const s=t.data.find(i=>i.id===parseInt(e));s&&s.offerid?this.offerId=s.offerid:t.data.length>0&&t.data[0].offerid&&(this.offerId=t.data[0].offerid)}else t&&t.data&&t.data.offercourseid?e==="3"&&(this.offerId=2):t&&t.offercourseid&&e==="3"&&(this.offerId=2);e==="3"&&!this.offerId&&(this.offerId=2)}catch{e==="3"&&(this.offerId=2)}await this.loadRoles(),await this.loadNameOptions(),await this.loadCpfOptions(),await this.loadEmailOptions(),await this.loadRegisteredUsers(e)}else this.error="ID da turma não encontrado"}catch(e){this.error="Erro ao carregar dados iniciais: "+e.message}},methods:{async loadRegisteredUsers(e){try{if(this.loading=!0,this.error=null,!e){this.error="ID da turma não fornecido",this.loading=!1;return}let t=[];if(this.selectedUserIds.name.length>0||this.selectedUserIds.cpf.length>0||this.selectedUserIds.email.length>0){const a=[...this.selectedUserIds.name,...this.selectedUserIds.cpf,...this.selectedUserIds.email];t=[...new Set(a)]}else if(this.hasActiveFilters)try{const a=[];this.appliedFilters.name&&this.appliedFilters.name.length>0&&this.appliedFilters.name.forEach(u=>{u.value&&!a.includes(u.value)&&a.push(u.value)}),this.appliedFilters.cpf&&this.appliedFilters.cpf.length>0&&this.appliedFilters.cpf.forEach(u=>{u.value&&!a.includes(u.value)&&a.push(u.value)}),this.appliedFilters.email&&this.appliedFilters.email.length>0&&this.appliedFilters.email.forEach(u=>{u.value&&!a.includes(u.value)&&a.push(u.value)}),a.length>0&&(t=a)}catch{}const i={offerclassid:parseInt(e),userids:t,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},n=await yu(i);if(n&&n.data){const a=n.data.data||n.data;if(Array.isArray(a.enrolments)){const u=await this.calculateDeadline();this.enrolments=a.enrolments.map(c=>({id:c.userid,offeruserenrolid:c.offeruserenrolid,fullName:c.fullname,email:c.email,cpf:c.cpf,enrol:c.enrol,roles:this.formatRoles(c.roles),groups:c.groups,timecreated:c.timecreated,createdDate:this.formatDateTime(c.timecreated),timestart:c.timestart,timeend:c.timeend,startDate:this.formatDate(c.timestart),endDate:this.formatDate(c.timeend),deadline:u==null?"Imilitado":u===1?"1 dia":`${u} dias`,progress:this.formatProgress(c.progress),situation:c.situation,situationName:c.situation_name,grade:c.grade||"-",status:c.status,statusName:c.status!==void 0?c.status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=a.total||this.enrolments.length}}else this.enrolments=[],this.totalEnrolments=0}catch(t){this.error="Erro ao carregar usuários matriculados: "+t.message,this.enrolments=[],this.totalEnrolments=0}finally{this.loading=!1}},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},async calculateDeadline(){const e=this.offerclassid||this.$route.params.offerclassid,t=await Lr(parseInt(e));if(t&&t.data){const s=t.data.optional_fields,i=s.enrolperiod,n=s.enableenrolperiod,a=s.enableenddate,u=t.data.startdate,c=s.enddate;let h;if(!a&&!n)return null;if(i===0&&n===!1)if(u&&c){const m=new Date(u),v=new Date(c)-m;h=Math.ceil(v/(1e3*60*60*24))}else h=null;else h=i;return h}return null},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").map(t=>this.translateAndCapitalizeRole(t.trim())).join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.map(t=>this.translateAndCapitalizeRole(t)).join(", "):"-"},translateAndCapitalizeRole(e){if(!e)return"";if(typeof e!="string")try{e=String(e)}catch{return"Papel desconhecido"}return e.charAt(0).toUpperCase()+e.slice(1)},async loadNameOptions(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e)return;const t=await bu({offerclassid:parseInt(e),filter_type:"name"});Array.isArray(t)?this.nameOptions=t.map(s=>({value:s.id,label:s.fullname})):this.nameOptions=[]}catch{this.nameOptions=[]}},async loadCpfOptions(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e)return;const t=await bu({offerclassid:parseInt(e),filter_type:"cpf"});Array.isArray(t)?this.cpfOptions=t.map(s=>({value:s.id,label:s.cpf})):this.cpfOptions=[]}catch{this.cpfOptions=[]}},async loadEmailOptions(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e)return;const t=await bu({offerclassid:parseInt(e),filter_type:"email"});Array.isArray(t)?this.emailOptions=t.map(s=>({value:s.id,label:s.email})):this.emailOptions=[]}catch{this.emailOptions=[]}},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},handleNameSelectNoFilter(e){if(this.inputFilters.name.findIndex(s=>s.value===e.value)===-1){const s={value:e.value,label:e.label};this.inputFilters.name.push(s),this.appliedFilters.name.push(s)}this.selectedName="",this.selectedUserIds.name.includes(e.value)||this.selectedUserIds.name.push(e.value)},handleCpfSelectNoFilter(e){if(this.inputFilters.cpf.findIndex(s=>s.value===e.value)===-1){const s={value:e.value,label:e.label};this.inputFilters.cpf.push(s),this.appliedFilters.cpf.push(s)}this.selectedCpf="",this.selectedUserIds.cpf.includes(e.value)||this.selectedUserIds.cpf.push(e.value)},handleEmailSelectNoFilter(e){if(this.inputFilters.email.findIndex(s=>s.value===e.value)===-1){const s={value:e.value,label:e.label};this.inputFilters.email.push(s),this.appliedFilters.email.push(s)}this.selectedEmail="",this.selectedUserIds.email.includes(e.value)||this.selectedUserIds.email.push(e.value)},handleNameSelect(e){this.handleNameSelectNoFilter(e),this.applyFilters()},handleCpfSelect(e){this.handleCpfSelectNoFilter(e),this.applyFilters()},handleEmailSelect(e){this.handleEmailSelectNoFilter(e),this.applyFilters()},async handlePageChange(e){if(e!==this.currentPage){this.currentPage=e;const t=this.offerclassid||this.$route.params.offerclassid;t?await this.loadRegisteredUsers(t):this.error="ID da turma não encontrado"}},async handlePerPageChange(e){if(e!==this.perPage){this.perPage=e,this.currentPage=1;const t=this.offerclassid||this.$route.params.offerclassid;t?await this.loadRegisteredUsers(t):this.error="ID da turma não encontrado"}},async applyFilters(){try{this.loading=!0,this.currentPage=1,this.selectedUsers=[],this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));const e=this.offerclassid||this.$route.params.offerclassid;e?await this.loadRegisteredUsers(e):this.error="ID da turma não encontrado"}catch(e){this.error=e.message}finally{this.loading=!1}},async clearFilters(){this.inputFilters={name:[],cpf:[],email:[]},this.appliedFilters={name:[],cpf:[],email:[]},this.selectedName="",this.selectedCpf="",this.selectedEmail="",this.selectedUserIds={name:[],cpf:[],email:[]},this.selectedUsers=[],this.currentPage=1;const e=this.offerclassid||this.$route.params.offerclassid;e?await this.loadRegisteredUsers(e):this.error="ID da turma não encontrado"},async removeFilter(e,t){t!==void 0?(this.appliedFilters[e].splice(t,1),this.inputFilters[e].splice(t,1),this.selectedUserIds[e][t]&&this.selectedUserIds[e].splice(t,1)):(this.inputFilters[e]=[],this.appliedFilters[e]=[],this.selectedUserIds[e]=[]),e==="name"?this.selectedName="":e==="cpf"?this.selectedCpf="":e==="email"&&(this.selectedEmail=""),this.selectedUsers=[],this.currentPage=1;const s=this.offerclassid||this.$route.params.offerclassid;s?await this.loadRegisteredUsers(s):this.error="ID da turma não encontrado"},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t;const s=this.offerclassid||this.$route.params.offerclassid;s&&await this.loadRegisteredUsers(s)},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var t;if(!(this.offerclassid||this.$route.params.offerclassid)){this.error="ID da turma não encontrado. Não é possível matricular usuários.";return}if(this.classDetails&&((t=this.classDetails)==null?void 0:t.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){try{const e=this.offerclassid||this.$route.params.offerclassid;if(!e){this.router.push({name:"offer-manager"});return}const t=await Lr(parseInt(e));if(!t||t.error){this.router.push({name:"offer-manager"});return}let s=null;if(t.data&&t.data.offercourseid)s=t.data.offercourseid;else if(t.offercourseid)s=t.offercourseid;else if(t.data&&Array.isArray(t.data)){const a=t.data.find(u=>u.id===parseInt(e));a&&a.offercourseid&&(s=a.offercourseid)}if(!s){this.router.push({name:"offer-manager"});return}const i=await gu(s);if(!i){this.router.push({name:"offer-manager"});return}let n=null;if(i.data[0].offerid&&(n=i.data[0].offerid),!n){this.router.push({name:"offer-manager"});return}this.router.push({name:"editar-oferta",params:{id:n.toString()}})}catch{this.router.push({name:"offer-manager"})}},viewUserProfile(e){var i;if(!e)return;const t=(i=this.classDetails)==null?void 0:i.courseid,s=`/user/view.php?id=${e}&course=${t}`;window.location.href=s},async handlePageViewChange(e){const t=this.offerclassid||this.$route.params.offerclassid;if(t)try{const s=await Lr(parseInt(t));if(!s||!s.data||!s.data.offercourseid){this.selectedPageView="usuarios_matriculados";return}const i=s.data.offercourseid;try{const n=await gu(i);if(!n||Array.isArray(n)&&n.length===0){this.selectedPageView="usuarios_matriculados";return}let a=null;s.data&&s.data.courseid?a=s.data.courseid:Array.isArray(n)&&n.length>0&&n[0]&&n[0].error===!1&&n[0].data&&n[0].data.courses&&n[0].data.courses.length>0&&(a=n[0].data.courses[0].courseid),a||(a=i);let u=null;s.data&&s.data.course_context_id?u=s.data.course_context_id:u=a;const c={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${a}`,agrupamentos:`/group/groupings.php?id=${a}`,visao_geral:`/user/index.php?id=${a}`,permissoes:`/admin/roles/permissions.php?contextid=${u}`,outros_usuarios:`/enrol/otherusers.php?id=${a}`,verificar_permissoes:`/admin/roles/check.php?contextid=${u}`};c[e]?window.location.href=c[e]:this.selectedPageView="usuarios_matriculados"}catch{this.selectedPageView="usuarios_matriculados"}}catch{this.selectedPageView="usuarios_matriculados"}},async handleEnrolmentSuccess(){const e=this.offerclassid||this.$route.params.offerclassid;e&&await this.loadRegisteredUsers(e)},async loadRoles(){try{const e=this.offerclassid||this.$route.params.offerclassid;try{const t=await Lr(parseInt(e));if(t&&t.data&&t.data.offercourseid){const s=t.data.offercourseid;try{const i=await ta(s);i&&Array.isArray(i)&&(this.roleOptions=i.map(n=>({id:String(n.id),name:n.name})))}catch(i){console.error("Erro ao buscar papéis do curso:",i)}}}catch(t){console.error("Erro ao obter detalhes da turma:",t)}}catch(e){console.error("Erro geral em loadRoles:",e)}},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(n=>n.id===String(e.roleid));i&&(t=i.name)}if(!t){const i=this.offerclassid||this.$route.params.offerclassid;if(i){await this.loadRegisteredUsers(i),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else{const i=this.offerclassid||this.$route.params.offerclassid;i&&await this.loadRegisteredUsers(i)}}else{const t=this.offerclassid||this.$route.params.offerclassid;t&&await this.loadRegisteredUsers(t)}this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){const e=this.offerclassid||this.$route.params.offerclassid;e?this.loadRegisteredUsers(e):this.error="ID da turma não encontrado"},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},generateCertificate(e){e.situation},async confirmeBulkDeleteEnrollment(){try{this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&n.offeruserenrolid?e.push(n.offeruserenrolid):console.error(`Não foi possível encontrar o ID da matrícula para o usuário ID ${i}`)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await rE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;if(i>0){this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`);const a=this.offerclassid||this.$route.params.offerclassid;a&&await this.loadRegisteredUsers(a),this.selectedUsers=[]}else this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else{this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`);const i=this.offerclassid||this.$route.params.offerclassid;i&&await this.loadRegisteredUsers(i),this.selectedUsers=[]}this.showBulkDeleteEnrollmentModal=!1}catch(e){this.error=e.message,this.showErrorMessage("Ocorreu um erro ao cancelar a matrícula. Por favor, tente novamente.")}finally{this.loading=!1}},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}let e=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(t=>typeof t=="number"))e=this.selectedUsers;else if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(t=>t&&typeof t=="object"))e=this.selectedUsers.filter(t=>t&&(t.id||t.userId)).map(t=>t.id||t.userId),console.log("IDs extraídos de objetos de usuário:",e);else try{e=this.selectedUsers.filter(t=>t!=null).map(t=>typeof t=="number"?t:typeof t=="object"&&t!==null?t.id||t.userId:null).filter(t=>t!=null),console.log("IDs extraídos com método alternativo:",e)}catch(t){console.error("Erro ao extrair IDs:",t)}if(e.length===0){this.showErrorMessage("Não foi possível enviar mensagem. Nenhum usuário válido selecionado.");return}this.showSendMessageModal(e)},showSendMessageModal(e){if(typeof window.require!="function"){console.error("Função require não disponível"),this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{console.error("Erro ao carregar o módulo core_message/message_send_bulk:",t),this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}console.log("Escrevendo anotação para usuários:",this.selectedUsers);let e=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))e=this.selectedUsers,console.log("selectedUsers é um array de IDs:",e);else if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>s&&typeof s=="object"))e=this.selectedUsers.filter(s=>s&&(s.id||s.userId)).map(s=>s.id||s.userId),console.log("IDs extraídos de objetos de usuário:",e);else try{e=this.selectedUsers.filter(s=>s!=null).map(s=>typeof s=="number"?s:typeof s=="object"&&s!==null?s.id||s.userId:null).filter(s=>s!=null)}catch(s){console.error("Erro ao extrair IDs:",s)}if(e.length===0){this.showErrorMessage("Não foi possível escrever anotação. Nenhum usuário válido selecionado.");return}const t=this.classDetails.courseid;if(!t){this.showErrorMessage("Não foi possível escrever anotação. ID do curso não disponível.");return}this.showAddNoteModal(t,e)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{console.log("Modal de anotações fechado"),this.selectedBulkAction=""}),n)).catch(n=>{console.error("Erro ao mostrar o modal de anotações:",n),this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{console.error(s),this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else for(const s of this.selectedUsers){if(!s){console.warn("Usuário inválido encontrado:",s);continue}if(typeof s=="number"){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}}if(t.length===0){if(console.error("Nenhum dado disponível para download após processamento"),this.selectedUsers.length>0&&this.enrolments&&this.enrolments.length>0){const s=this.selectedUsers.map((i,n)=>({ID:i,Índice:n+1,Selecionado:"Sim"}));if(s.length>0){switch(e){case"csv":this.downloadCSV(s);break;case"xlsx":this.downloadXLSX(s);break;case"html":this.downloadHTML(s);break;case"json":this.downloadJSON(s);break;default:this.showErrorMessage("Formato de download não suportado.");break}return}}this.showErrorMessage("Nenhum dado disponível para download.");return}try{switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}}catch(s){console.error("Erro ao fazer download:",s),this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,m=>m.toUpperCase()).trim()),n=t+[i.join(","),...e.map(h=>s.map(m=>{const p=h[m]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const m=c[h]||"";return`"${String(m).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length!==0)try{const t=Object.keys(e[0]),s=[];for(let T=0;T<t.length;T++){const se=t[T].replace(/([A-Z])/g," $1").replace(/^./,G=>G.toUpperCase()).trim();s.push(se)}let i="";for(let T=0;T<s.length;T++)i+="<th>"+s[T]+"</th>";let n="";for(let T=0;T<e.length;T++){let se="<tr>";for(let G=0;G<t.length;G++)se+="<td>"+(e[T][t[G]]||"")+"</td>";se+="</tr>",n+=se}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",m="</tr></thead><tbody>",p="</tbody></table>",v='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",D=a+u+c+h+i+m+n+p+v+w,k=new Blob([D],{type:"text/html;charset=utf-8;"}),U=URL.createObjectURL(k),te=document.createElement("a");te.setAttribute("href",U),te.setAttribute("download","usuarios_matriculados.html"),te.style.visibility="hidden",document.body.appendChild(te),te.click(),document.body.removeChild(te),URL.revokeObjectURL(U),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")}catch{this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length!==0)try{const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const m=s.map(p=>{const v=h[p]||"";return'"'+String(v).replace(/"/g,'""')+'"'});i.push(m.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")}catch{this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},downloadPDF(e){if(e.length!==0)try{this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF.")}catch{this.showErrorMessage("Ocorreu um erro ao fazer o download. Por favor, tente novamente.")}},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(console.log("Editando matrículas dos usuários:",this.selectedUsers),this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){console.log("Matrículas editadas em lote com sucesso:",e),this.showSuccessMessage(e.message||"Matrículas editadas com sucesso.");const t=this.offerclassid||this.$route.params.offerclassid;t&&await this.loadRegisteredUsers(t),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},hx={id:"offer-manager-component",class:"offer-manager"},px={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},mx={style:{width:"240px"}},gx={class:"filter-buttons"},vx={key:0,class:"alert alert-danger"},_x={class:"table-container"},yx={class:"checkbox-container"},bx=["checked","indeterminate"],wx={class:"checkbox-container"},Ex=["checked","onChange"],Cx=["href","title"],Dx={class:"user-name-link"},xx={class:"progress-container"},Sx={class:"progress-text"},Ox={class:"status-container"},Ix={class:"status-actions"},Nx=["onClick"],Tx=["onClick"],Ax={class:"selected-users-actions"},Mx={class:"bulk-actions-container"},Px={key:1,class:"bottom-enroll-button"};function kx(e,t,s,i,n,a){var ke,ae,We;const u=X("BackButton"),c=X("PageHeader"),h=X("HierarchicalSelect"),m=X("CustomButton"),p=X("Autocomplete"),v=X("FilterGroup"),w=X("FilterRow"),D=X("FilterTag"),k=X("FilterTags"),U=X("FilterSection"),te=X("UserAvatar"),T=X("RoleSelector"),se=X("CustomTable"),G=X("Pagination"),we=X("EnrollmentDetailsModal"),Q=X("EnrolmentModalNew"),he=X("EditEnrollmentModal"),be=X("BulkEditEnrollmentModal"),Ae=X("BulkDeleteEnrollmentModal"),le=X("LFLoading"),ie=X("Toast");return O(),N("div",hx,[A(c,{title:"Usuários matriculados"},{actions:ye(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),f("div",px,[f("div",mx,[A(h,{modelValue:n.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=J=>n.selectedPageView=J),options:n.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!n.classDetails||((ke=n.classDetails)==null?void 0:ke.operational_cycle)!==2?(O(),Pt(m,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):ce("",!0)]),A(U,{"has-active-tags":a.hasActiveFilters,title:""},{tags:ye(()=>[A(k,null,{default:ye(()=>[(O(!0),N(Me,null,mt(n.appliedFilters.name,(J,$e)=>(O(),Pt(D,{key:"name-"+$e,onRemove:ut=>a.removeFilter("name",$e)},{default:ye(()=>[tt(" Nome: "+W(J.label),1)]),_:2},1032,["onRemove"]))),128)),(O(!0),N(Me,null,mt(n.appliedFilters.cpf,(J,$e)=>(O(),Pt(D,{key:"cpf-"+$e,onRemove:ut=>a.removeFilter("cpf",$e)},{default:ye(()=>[tt(" CPF: "+W(J.label),1)]),_:2},1032,["onRemove"]))),128)),(O(!0),N(Me,null,mt(n.appliedFilters.email,(J,$e)=>(O(),Pt(D,{key:"email-"+$e,onRemove:ut=>a.removeFilter("email",$e)},{default:ye(()=>[tt(" E-mail: "+W(J.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})]),default:ye(()=>[A(w,{inline:!0},{default:ye(()=>[A(v,{label:"Filtrar por nome"},{default:ye(()=>[A(p,{modelValue:n.selectedName,"onUpdate:modelValue":t[1]||(t[1]=J=>n.selectedName=J),items:a.filteredNameOptions,placeholder:"Buscar...",width:"100%",onSelect:a.handleNameSelectNoFilter,"has-search-icon":!1,class:"custom-autocomplete"},null,8,["modelValue","items","onSelect"])]),_:1}),A(v,{label:"Filtrar por CPF"},{default:ye(()=>[A(p,{modelValue:n.selectedCpf,"onUpdate:modelValue":t[2]||(t[2]=J=>n.selectedCpf=J),items:a.filteredCpfOptions,placeholder:"Buscar...",width:"100%",onSelect:a.handleCpfSelectNoFilter,"has-search-icon":!1,class:"custom-autocomplete"},null,8,["modelValue","items","onSelect"])]),_:1}),A(v,{label:"Filtrar por E-mail"},{default:ye(()=>[A(p,{modelValue:n.selectedEmail,"onUpdate:modelValue":t[3]||(t[3]=J=>n.selectedEmail=J),items:a.filteredEmailOptions,placeholder:"Buscar...",width:"100%",onSelect:a.handleEmailSelectNoFilter,"has-search-icon":!1,class:"custom-autocomplete"},null,8,["modelValue","items","onSelect"])]),_:1}),A(v,null,{default:ye(()=>[f("div",gx,[A(m,{variant:"primary",label:"Filtrar",onClick:a.applyFilters},null,8,["onClick"]),A(m,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])])]),_:1})]),_:1})]),_:1},8,["has-active-tags"]),n.error?(O(),N("div",vx,[t[11]||(t[11]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),tt(" "+W(n.error),1)])):ce("",!0),f("div",_x,[A(se,{headers:n.tableHeaders,items:n.enrolments,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"header-select":ye(()=>[f("div",yx,[f("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[4]||(t[4]=(...J)=>a.toggleSelectAll&&a.toggleSelectAll(...J)),class:"custom-checkbox"},null,40,bx)])]),"item-select":ye(({item:J})=>[f("div",wx,[f("input",{type:"checkbox",checked:a.isSelected(J.id),onChange:$e=>a.toggleSelectUser(J.id),class:"custom-checkbox"},null,40,Ex)])]),"item-fullName":ye(({item:J})=>[f("a",{class:"user-name-container",href:`/user/view.php?id=${J.id}`,title:"Ver perfil de "+J.fullName},[A(te,{"full-name":J.fullName,size:36},null,8,["full-name"]),f("span",Dx,W(J.fullName),1)],8,Cx)]),"item-email":ye(({item:J})=>[tt(W(J.email),1)]),"item-cpf":ye(({item:J})=>[tt(W(J.cpf),1)]),"item-roles":ye(({item:J})=>[A(T,{userId:J.id,offeruserenrolid:J.offeruserenrolid,currentRole:J.roles,offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":ye(({item:J})=>[tt(W(J.groups),1)]),"item-startDate":ye(({item:J})=>[tt(W(J.startDate),1)]),"item-endDate":ye(({item:J})=>[tt(W(J.endDate),1)]),"item-deadline":ye(({item:J})=>[tt(W(J.deadline),1)]),"item-progress":ye(({item:J})=>[f("div",xx,[f("div",{class:"progress-bar",style:ls({width:J.progress})},null,4),f("span",Sx,W(J.progress),1)])]),"item-situation":ye(({item:J})=>[tt(W(J.situationName),1)]),"item-grade":ye(({item:J})=>[tt(W(J.grade),1)]),"item-status":ye(({item:J})=>[f("div",Ox,[f("span",{class:pe(["status-tag badge",J.status===0?"badge-success":"badge-danger"])},W(J.statusName),3),f("div",Ix,[f("button",{class:"btn-information",onClick:$e=>a.showEnrollmentDetails(J),title:"Informações da matrícula"},t[12]||(t[12]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,Nx),f("button",{class:"btn-settings",onClick:$e=>a.editUser(J),title:"Editar matrícula"},t[13]||(t[13]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"3"}),f("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,Tx)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),A(G,{"current-page":n.currentPage,"onUpdate:currentPage":t[5]||(t[5]=J=>n.currentPage=J),"per-page":n.perPage,"onUpdate:perPage":t[6]||(t[6]=J=>n.perPage=J),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),f("div",Ax,[f("div",Mx,[t[15]||(t[15]=f("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),vt(f("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[7]||(t[7]=J=>n.selectedBulkAction=J),onChange:t[8]||(t[8]=(...J)=>a.handleBulkAction&&a.handleBulkAction(...J))},t[14]||(t[14]=[ly('<option value="" data-v-836aa043>Escolher...</option><optgroup label="Comunicação" data-v-836aa043><option value="message" data-v-836aa043>Enviar uma mensagem</option><option value="note" data-v-836aa043>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-836aa043><option value="download_csv" data-v-836aa043> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-836aa043>Microsoft excel (.xlsx)</option><option value="download_html" data-v-836aa043>Tabela HTML</option><option value="download_json" data-v-836aa043> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-836aa043>OpenDocument (.ods)</option><option value="download_pdf" data-v-836aa043> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-836aa043><option value="edit_enrolment" data-v-836aa043> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-836aa043> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Zl,n.selectedBulkAction]])])]),!n.classDetails||((ae=n.classDetails)==null?void 0:ae.operational_cycle)!==2?(O(),N("div",Px,[A(m,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):ce("",!0),A(we,{show:n.showEnrollmentModal,user:n.selectedUser,"course-name":((We=n.classDetails)==null?void 0:We.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),A(Q,{show:n.showEnrolmentModal,offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),roles:n.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),A(he,{show:n.showEditEnrollmentModal,user:n.selectedUser,offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),A(be,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(J=>n.enrolments.find($e=>$e.id===J)).filter(Boolean),offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onClose:t[9]||(t[9]=J=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),A(Ae,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(J=>n.enrolments.find($e=>$e.id===J)).filter(Boolean),offerclassid:parseInt(s.offerclassid||e.$route.params.offerclassid),onClose:t[10]||(t[10]=J=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),A(le,{"is-loading":n.loading},null,8,["is-loading"]),A(ie,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Rx=ze(fx,[["render",kx],["__scopeId","data-v-836aa043"]]),OV="",Vx={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},Fx={class:"table-responsive"},Ux={class:"table"},Lx={key:0,class:"expand-column"},Bx=["onClick","data-value"],$x={key:0,class:"sort-icon"},jx={key:0},Hx={key:0,class:"expand-column"},qx=["onClick","title"],zx=["colspan"],Wx={class:"expanded-content"},Gx={key:1},Kx=["colspan"];function Yx(e,t,s,i,n,a){return O(),N("div",Fx,[f("table",Ux,[f("thead",null,[f("tr",null,[s.expandable?(O(),N("th",Lx)):ce("",!0),(O(!0),N(Me,null,mt(s.headers,u=>(O(),N("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:pe({sortable:u.sortable}),"data-value":u.value},[tt(W(u.text)+" ",1),u.sortable?(O(),N("span",$x,[f("i",{class:pe(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):ce("",!0)],10,Bx))),128))])]),s.items.length>0?(O(),N("tbody",jx,[(O(!0),N(Me,null,mt(s.items,(u,c)=>(O(),N(Me,{key:u.id},[f("tr",{class:pe({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(O(),N("td",Hx,[f("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[f("div",{class:pe(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[f("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[f("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),f("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,qx)])):ce("",!0),(O(!0),N(Me,null,mt(s.headers,h=>(O(),N("td",{key:`${u.id}-${h.value}`},[Vt(e.$slots,"item-"+h.value,{item:u},()=>[tt(W(u[h.value]),1)],!0)]))),128))],2),s.expandable?(O(),N("tr",{key:0,class:pe(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[f("td",{colspan:s.headers.length+1},[f("div",Wx,[Vt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,zx)],2)):ce("",!0)],64))),128))])):(O(),N("tbody",Gx,[f("tr",null,[f("td",{colspan:s.headers.length+(s.expandable?1:0)},[Vt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=f("div",{class:"empty-state"},[f("span",null,"Não existem registros")],-1))],!0)],8,Kx)])]))])])}const Qx=ze(Vx,[["render",Yx],["__scopeId","data-v-049f598f"]]),IV="",Zx={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},Jx={class:"text-editor-container"},Xx={class:"editor-toolbar"},eS={class:"toolbar-group"},tS=["disabled"],sS=["disabled"],rS=["disabled"],nS=["disabled"],oS={class:"toolbar-group"},iS=["disabled"],aS=["disabled"],lS=["contenteditable"],uS=["rows","placeholder","disabled"];function cS(e,t,s,i,n,a){return O(),N("div",Jx,[s.label?(O(),N("label",{key:0,class:pe(["filter-label",{disabled:s.disabled}])},W(s.label),3)):ce("",!0),f("div",{class:pe(["editor-container",{disabled:s.disabled}])},[f("div",Xx,[f("div",eS,[f("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[f("i",{class:"fas fa-bold"},null,-1)]),8,tS),f("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[f("i",{class:"fas fa-italic"},null,-1)]),8,sS),f("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[f("i",{class:"fas fa-underline"},null,-1)]),8,rS),f("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[f("i",{class:"fas fa-strikethrough"},null,-1)]),8,nS)]),t[16]||(t[16]=f("div",{class:"toolbar-divider"},null,-1)),f("div",oS,[f("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[f("i",{class:"fas fa-list-ul"},null,-1)]),8,iS),f("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[f("i",{class:"fas fa-list-ol"},null,-1)]),8,aS)])]),n.showHtmlSource?vt((O(),N("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,uS)),[[ws,n.htmlContent]]):(O(),N("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,lS))],2)])}const dp=ze(Zx,[["render",cS],["__scopeId","data-v-672cb06c"]]),NV="",TV="",dS={name:"AddCourseModal",components:{CustomInput:Lo,CustomButton:Bn,CustomTable:pn,Pagination:mn,Autocomplete:jo,FilterTag:$o},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCategoryObject:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],existingCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e){e?(this.loadCurrentCourses(),this.loadAllCategories()):(this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){e||(this.courseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null,this.loadCurrentCourses())},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.selectedCoursesPreview=[]},async confirm(){try{if(this.selectedCoursesPreview.length===0){console.warn("Nenhum curso selecionado para adicionar"),this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await i1(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch(e){console.error("Erro ao salvar cursos:",e)}},async loadCurrentCourses(){console.log("loadCurrentCourses this.offerId:",this.offerId);try{this.loadingCurrentCourses=!0;const e=await ea(this.offerId);console.log("loadCurrentCourses response:",e),e&&e.data&&(Array.isArray(e.data)?this.existingCourses=e.data.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id})):this.existingCourses=[])}catch(e){console.error("Erro ao carregar cursos da oferta:",e),this.existingCourses=[]}finally{this.loadingCurrentCourses=!1}},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Uo("");e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar todas as categorias:",e),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCoursesForCategory(e.value)},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const a=await ip(this.offerId,n,i,t,this.coursesPerPage);let u=null,c=[];try{if(Array.isArray(a)&&a.length>0?a[0].error===!1&&a[0].data?a[0].data.courses?(u=a[0].data,c=u.courses||[]):Array.isArray(a[0].data)?(c=a[0].data,u={page:1,total_pages:1}):a[0].data.data&&Array.isArray(a[0].data.data)&&(c=a[0].data.data,u={page:a[0].data.page||1,total_pages:a[0].data.total_pages||1}):(c=a,u={page:1,total_pages:1}):a&&typeof a=="object"&&(a.data&&a.data.courses?(c=a.data.courses,u={page:a.data.page||1,total_pages:a.data.total_pages||1}):a.courses?(c=a.courses,u={page:a.page||1,total_pages:a.total_pages||1}):a.data&&Array.isArray(a.data)&&(c=a.data,u={page:1,total_pages:1})),c.length===0&&a&&typeof a=="object"&&!Array.isArray(a)){for(const h in a)if(Array.isArray(a[h])){c=a[h],u={page:1,total_pages:1};break}}}catch(h){console.error("Erro ao processar resposta:",h)}if(u){if(this.coursesPage=u.page||1,this.coursesTotalPages=u.total_pages||1,this.hasMoreCourses=(u.page||1)<(u.total_pages||1),c&&c.length>0){const m=c.filter(p=>!this.existingCourses.some(v=>v.id===p.id)&&!this.selectedCoursesPreview.some(v=>v.id===p.id)).map(p=>({value:p.id,label:p.fullname}));s?this.courseOptions=[...this.courseOptions,...m]:this.courseOptions=m}}else console.warn("Formato de resposta inesperado")}catch(a){console.error("Erro ao carregar cursos da categoria:",a),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(console.log("Buscando cursos com termo:",e),this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?ip(this.offerId,this.selectedCategory,"",1,this.coursesPerPage).then(i=>{let n=[];try{if(Array.isArray(i)&&i.length>0?i[0].error===!1&&i[0].data?i[0].data.courses?n=i[0].data.courses||[]:Array.isArray(i[0].data)?n=i[0].data:i[0].data.data&&Array.isArray(i[0].data.data)&&(n=i[0].data.data):n=i:i&&typeof i=="object"&&(i.data&&i.data.courses?n=i.data.courses:i.courses?n=i.courses:i.data&&Array.isArray(i.data)&&(n=i.data)),n.length===0&&i&&typeof i=="object"&&!Array.isArray(i)){for(const a in i)if(Array.isArray(i[a])){n=i[a];break}}}catch(a){console.error("Erro ao processar resposta em removeCourse:",a)}n&&n.length>0&&n.find(u=>u.id===s.id)&&this.courseOptions.push({value:s.id,label:s.name})}).catch(i=>{console.error("Erro ao verificar categoria do curso:",i)}):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.courseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCurrentCourses()}}},fS={class:"modal-header"},hS={class:"modal-body"},pS={class:"search-section"},mS={class:"search-group"},gS={class:"search-group"},vS={class:"table-container"},_S={key:0,class:"empty-preview-message"},yS={class:"action-buttons"},bS=["onClick"],wS={class:"modal-footer"};function ES(e,t,s,i,n,a){const u=X("Autocomplete"),c=X("CustomTable"),h=X("Pagination"),m=X("CustomButton");return s.modelValue?(O(),N("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[f("div",{class:"modal-content",onClick:t[5]||(t[5]=Ft(()=>{},["stop"]))},[f("div",fS,[t[8]||(t[8]=f("h2",null,"Adicionar curso",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",hS,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",pS,[f("div",mS,[A(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",gS,[A(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",vS,[n.selectedCoursesPreview.length===0?(O(),N("div",_S,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(O(),Pt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":ye(({item:p})=>[f("div",yS,[f("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,bS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(O(),Pt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>n.currentPage=p),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>n.perPage=p),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):ce("",!0)]),f("div",wS,[A(m,{variant:"primary",label:"Confirmar",disabled:n.selectedCoursesPreview.length===0,onClick:a.confirm},null,8,["disabled","onClick"]),A(m,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):ce("",!0)}const CS=ze(dS,[["render",ES],["__scopeId","data-v-0a88ee2a"]]),AV="",MV="",DS={name:"DuplicateClassModal",components:{Autocomplete:jo,CustomTable:pn,Pagination:mn},props:{show:{type:Boolean,default:!1},turma:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,selectedCategoryObject:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return[...this.selectedCoursesPreview].sort((i,n)=>{const a=i[this.sortBy],u=n[this.sortBy];return a<u?this.sortDesc?1:-1:a>u?this.sortDesc?-1:1:0}).slice(e,t)}},watch:{show(e){e&&this.turma&&this.parentCourse?(this.resetForm(),this.$nextTick(()=>{this.loadAllCategories()})):this.resetForm()},turma(){this.show&&(this.resetForm(),this.loadAllCategories())},parentCourse(){this.show&&(this.resetForm(),this.loadAllCategories())},selectedCategory(e){e?this.loadCoursesForCategory(e):(this.targetCourseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null)}},methods:{resetForm(){this.selectedCategory=null,this.selectedCategoryObject=null,this.categoryOptions=[],this.loadingCategories=!1,this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.coursesPage=1,this.coursesPerPage=20,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.currentPage=1,this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0,this.existingCourses=[]},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Uo("",this.offerId);e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar categorias:",e),this.$emit("error","Erro ao carregar categorias. Por favor, tente novamente."),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.targetCourseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.targetCourseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e||!this.turma)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const a=await p1(this.turma.id);let u=[];a&&Array.isArray(a)&&(u=a);const c=this.parentCourse.offerCourseId||this.parentCourse.id;u=u.filter(m=>{const p=m.categoryid,v=String(p)===String(n),w=m.offercourseid||m.id,D=String(w)!==String(c),k=!i||m.name&&m.name.toLowerCase().includes(i.toLowerCase())||m.fullname&&m.fullname.toLowerCase().includes(i.toLowerCase());return v&&D&&k}),u=u.filter(m=>{const p=m.offercourseid||m.id;return!this.selectedCoursesPreview.some(v=>String(v.value)===String(p))});const h=u.map(m=>{let p=m.offercourseid||m.id;return p==null?null:{value:p,label:m.name||m.fullname||m.coursename||`Curso ${p}`,categoryid:m.categoryid,category_name:m.category_name}}).filter(m=>m!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...h]:this.targetCourseOptions=h,this.hasMoreCourses=h.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(a){console.error("Erro ao carregar cursos da categoria:",a),this.$emit("error","Erro ao carregar cursos. Por favor, tente novamente."),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value)),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},async handleConfirm(){if(!(!this.turma||this.selectedCoursesPreview.length===0))try{this.$emit("loading",!0);const e=this.turma.nome,t=parseInt(this.turma.id,10);if(isNaN(t))throw new Error("ID da turma inválido");this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value,10);if(isNaN(n)){console.error(`ID inválido para o curso ${i.label}`);continue}try{this.$emit("loading",!0,`Duplicando para ${i.label} (${this.duplicatedCount+1}/${this.totalToDuplicate})`);const a=await h1(t,n);s.push({turmaNome:e,targetCourseName:i.label,turmaId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){console.error(`Erro ao duplicar para o curso ${i.label}:`,a),this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(this.duplicatingCourses=!1,s.length>0)this.$emit("success",{turmaNome:e,duplicatedCount:s.length,totalSelected:this.totalToDuplicate,results:s}),this.resetForm(),this.$emit("close");else throw new Error("Nenhuma turma foi duplicada com sucesso.")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.$emit("loading",!1)}}}},xS={class:"modal-header"},SS={class:"modal-title"},OS={class:"modal-body"},IS={class:"search-section"},NS={class:"search-group"},TS={class:"search-group"},AS={class:"table-container"},MS={key:0,class:"empty-preview-message"},PS={class:"action-buttons"},kS=["onClick"],RS={class:"modal-footer"},VS=["disabled"];function FS(e,t,s,i,n,a){var m;const u=X("Autocomplete"),c=X("CustomTable"),h=X("Pagination");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[8]||(t[8]=p=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[7]||(t[7]=Ft(()=>{},["stop"]))},[f("div",xS,[f("h3",SS,'Duplicar Turma "'+W((m=s.turma)==null?void 0:m.nome)+'"',1),f("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[9]||(t[9]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",OS,[t[12]||(t[12]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",IS,[f("div",NS,[A(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",TS,[A(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",AS,[n.selectedCoursesPreview.length===0?(O(),N("div",MS,t[10]||(t[10]=[f("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(O(),Pt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":ye(({item:p})=>[f("div",PS,[f("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[11]||(t[11]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,kS)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(O(),Pt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=p=>n.currentPage=p),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=p=>n.perPage=p),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):ce("",!0)]),f("div",RS,[f("button",{class:"btn-primary",onClick:t[5]||(t[5]=(...p)=>a.handleConfirm&&a.handleConfirm(...p)),disabled:n.selectedCoursesPreview.length===0}," Duplicar ",8,VS),f("button",{class:"btn-secondary",onClick:t[6]||(t[6]=p=>e.$emit("close"))},"Cancelar")])])])):ce("",!0)}const US=ze(DS,[["render",FS],["__scopeId","data-v-7ebf3397"]]),PV="",LS={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},BS=["data-content","aria-label"],$S=["title","aria-label"];function jS(e,t,s,i,n,a){return O(),N("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[f("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,$S)],8,BS)}const Eu=ze(LS,[["render",jS]]),kV="",HS={name:"EnrolTypeModal",components:{CustomSelect:mr,HelpIcon:Eu},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offercourseid:{type:[Number,String],required:!0},offerid:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){this.loading=!0;const e=await lp(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name}))),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offercourseid:this.offercourseid,offerid:this.offerid})},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},qS={class:"modal-header"},zS={class:"modal-title"},WS={class:"modal-body"},GS={class:"enrol-type-modal"},KS={class:"form-group mb-3"},YS={class:"label-with-help"},QS={class:"limited-width-input",style:{"max-width":"280px"}},ZS={class:"modal-footer"},JS={class:"footer-buttons"},XS=["disabled"];function eO(e,t,s,i,n,a){const u=X("HelpIcon"),c=X("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:pe(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Ft(()=>{},["stop"]))},[f("div",qS,[f("h3",zS,W(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",WS,[f("div",GS,[t[9]||(t[9]=f("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),f("div",KS,[f("div",YS,[t[7]||(t[7]=f("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),f("div",QS,[A(c,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...n.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),f("div",ZS,[t[10]||(t[10]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),f("div",JS,[f("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!n.selectedEnrolType},W(s.confirmButtonText),9,XS),f("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},W(s.cancelButtonText),1)])])],2)])):ce("",!0)}const tO=ze(HS,[["render",eO],["__scopeId","data-v-4b89966a"]]),sO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik03IDE0cy0xIDAtMS0xIDEtNCA1LTQgNSAzIDUgNC0xIDEtMSAxSDd6bTQtNmEzIDMgMCAxIDAgMC02IDMgMyAwIDAgMCAwIDZ6IiBmaWxsPSIjZmZmIi8+CiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUuMjE2IDE0QTIuMjM4IDIuMjM4IDAgMCAxIDUgMTNjMC0xLjM1NS42OC0yLjc1IDEuOTM2LTMuNzJBNi4zMjUgNi4zMjUgMCAwIDAgNSA5Yy00IDAtNSAzLTUgNHMxIDEgMSAxaDQuMjE2eiIgZmlsbD0iI2ZmZiIvPgogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",rO="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAyYS41LjUgMCAwIDEgLjUuNXY1aDVhLjUuNSAwIDAgMSAwIDFoLTV2NWEuNS41IDAgMCAxLTEgMHYtNWgtNWEuNS41IDAgMSAxIDAtMWg1di01QS41LjUgMCAwIDEgOCAyeiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",RV="",nO={name:"NewOfferView",components:{CustomTable:pn,CustomSelect:mr,CustomInput:Lo,CustomButton:Bn,Pagination:mn,CollapsibleTable:Qx,PageHeader:oa,BackButton:wu,Autocomplete:jo,TextEditor:dp,CustomCheckbox:sa,FilterRow:ra,FilterGroup:na,FilterTag:$o,FilterTags:ia,AddCourseModal:CS,ConfirmationModal:_u,Toast:Bo,HelpIcon:Eu,DuplicateClassModal:US,EnrolTypeModal:tO,LFLoading:vu},setup(){const e=Ji(),t=ep();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},data(){return{icons:{users:sO,plus:rO},showAddCourseModalVisible:!1,showCourseStatusModal:!1,selectedCourse:null,showWarning:!0,isEditing:!1,offerId:null,showDeleteCourseModal:!1,courseToDelete:null,showDeleteClassModal:!1,classToDelete:null,classParentCourse:null,showClassStatusModal:!1,showDuplicateClassModal:!1,showEnrolTypeModal:!1,selectedClass:null,classToDuplicate:null,classToDuplicateParentCourse:null,selectedCourseForClass:null,loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,offer:{id:null,name:"",offerType:"",description:"",status:0},formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}},offerTypeOptions:[],typeOptionsEnabled:!1,audienceTypeOptions:[],allAudiences:[],selectedAudiences:[],categoryOptions:[],courseOptions:[],selectedCategory:null,selectedCourse:null,inputFilters:{course:"",category:"",onlyActive:!1},appliedFilters:{course:"",category:"",onlyActive:!1},courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"turmasCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],selectedCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,courseNoResultsText:"Nenhum curso encontrado"}},async mounted(){await this.loadInitialData(),this.offerId?(this.isEditing=!0,await this.loadOfferData(),await this.loadCategoryOptions(),await this.loadCourseOptions()):this.isEditing=!1,await this.loadTypeOptions(),await this.loadAllAudiences(),this.showWarning=!0},computed:{canManageCourses(){return this.isEditing&&this.offerId!==null},hasActiveFilters(){return this.appliedFilters.course}},methods:{getEnrolTypeLabel(e){if(!e)return"-";if(typeof e!="string")return String(e);const t=e.toLowerCase(),s={offer_manual:"Manual",offer_self:"Autoinscrição",manual:"Manual",self:"Autoinscrição",guest:"Visitante",cohort:"Coorte",database:"Base de dados",flatfile:"Arquivo plano",ldap:"LDAP",lti:"LTI",meta:"Meta-curso",mnet:"MNet",paypal:"PayPal",shibboleth:"Shibboleth"};if(s[e])return s[e];for(const[i,n]of Object.entries(s))if(i.toLowerCase()===t)return n;if(t==="offer_manual")return"Manual";if(t==="offer_self")return"Autoinscrição";for(const[i,n]of Object.entries(s))if(t.includes(i.toLowerCase()))return n;return e},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},async loadInitialData(){},async loadOfferData(){if(this.offerId){this.loading=!0;try{const e=await rp(this.offerId);e&&e.data&&(this.offer={id:e.data.id,name:e.data.name,offerType:e.data.typeid,description:e.data.description||"",status:e.data.status||0},this.selectedAudiences=e.data.audiences?e.data.audiences.map(t=>({value:t.id,label:t.name})):[],await this.loadCourses())}catch{this.showErrorMessage("Erro ao carregar dados da oferta.")}finally{this.loading=!1}}},async loadTypeOptions(){try{const e=await pu();if(e&&e.data){const{enabled:t,types:s}=e.data;this.typeOptionsEnabled=!!t,t&&Array.isArray(s)?this.offerTypeOptions=s.map(i=>({value:i,label:i.charAt(0).toUpperCase()+i.slice(1)})):(this.offerTypeOptions=[],this.typeOptionsEnabled=!1)}else this.offerTypeOptions=[],this.typeOptionsEnabled=!1}catch{this.typeOptionsEnabled=!1}},async loadAllAudiences(){if(this.allAudiences.length>0){this.audienceTypeOptions=[...this.allAudiences];return}this.loading=!0;try{const e=await op("");e&&e.items&&(this.allAudiences=e.items.map(t=>({value:t.id,label:t.name})),this.audienceTypeOptions=[...this.allAudiences])}catch{this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},async applyFilters(){this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));try{this.loading=!0,this.appliedFilters.course&&this.appliedFilters.category&&this.appliedFilters.onlyActive?await this.loadCourses():this.appliedFilters.course?await this.loadCourses():this.appliedFilters.category&&await this.loadCourses()}catch{this.showErrorMessage("Erro ao aplicar filtros. Por favor, tente novamente.")}finally{this.loading=!1}this.currentPage=1},clearFilters(){this.inputFilters={course:"",category:"",onlyActive:!1},this.appliedFilters={course:"",category:"",onlyActive:!1},this.selectedCategory=null,this.selectedCourse=null,this.loadCourses(),this.loadCourseOptions()},async removeFilter(e){try{this.loading=!0,this.inputFilters[e]="",this.appliedFilters[e]="",e==="category"?(this.selectedCategory=null,await this.loadCourseOptions()):e==="course"&&(this.selectedCourse=null),this.hasActiveFilters?await this.applyFilters():await this.loadCourses()}catch{this.showErrorMessage("Erro ao remover filtro. Por favor, tente novamente.")}finally{this.loading=!1}},async loadCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Uo("",this.offerId);e&&e.data&&Array.isArray(e.data)?this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})):this.categoryOptions=[]}catch{this.showErrorMessage("Erro ao carregar categorias.")}finally{this.loading=!1}},async loadCourseOptions(e=null,t=!0){if(this.offerId)try{if(this.loading=!0,e){await this.updateCourseOptionsByCategory(e);return}t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const s=await ea(this.offerId,{onlyActive:!1,page:this.coursesPage,perPage:this.coursesPerPage,sortBy:this.sortBy,sortDesc:this.sortDesc});if(s&&s.data&&s.data.courses){const{page:i,total_pages:n,courses:a}=s.data;this.coursesPage=i||this.coursesPage,this.coursesTotalPages=n||1,this.hasMoreCourses=this.coursesPage<this.coursesTotalPages;const u=a.map(c=>({value:c.id||c.courseid,label:c.fullname}));t?this.courseOptions=u:this.courseOptions=[...this.courseOptions,...u],this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else if(s&&s.data&&Array.isArray(s.data)){const i=s.data.map(n=>({value:n.id||n.courseid,label:n.fullname}));t?this.courseOptions=i:this.courseOptions=[...this.courseOptions,...i],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else t&&(this.courseOptions=[]),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível"}catch{this.showErrorMessage("Erro ao carregar cursos."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}},async loadMoreCourses(){this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.coursesPage+=1,this.selectedCategory?await this.updateCourseOptionsByCategory({value:this.selectedCategory,label:this.inputFilters.category},!1):await this.loadCourseOptions(null,!1))},async handleCourseSearch(e){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[];try{if(this.selectedCategory){const t=this.selectedCategory,s=await mu(this.offerId,t);if(s&&s.data&&Array.isArray(s.data)){const i=s.data.filter(n=>n.fullname.toLowerCase().includes(e.toLowerCase()));this.courseOptions=i.map(n=>({value:n.id||n.courseid,label:n.fullname}))}}else{const t=await ap(this.offerId,e);t&&t.data&&Array.isArray(t.data)&&(this.courseOptions=t.data.map(s=>({value:s.id||s.courseid,label:s.fullname})))}this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso encontrado para a busca":"Nenhum curso encontrado"}catch{this.showErrorMessage("Erro ao buscar cursos."),this.courseOptions=[]}finally{this.loadingCourses=!1}},async updateCourseOptionsByCategory(e,t=!0){if(!(!this.offerId||!e)){this.loading=!0;try{t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;let s=e.value;if(isNaN(parseInt(s))){const n=await Uo(e.label,this.offerId);if(n&&n.data&&n.data.length>0){const a=n.data.find(u=>u.name.toLowerCase()===e.label.toLowerCase());if(a)s=a.id;else{this.showErrorMessage("Erro ao identificar a categoria selecionada."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}else{this.showErrorMessage("Erro ao buscar categorias."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}const i=await mu(this.offerId,s);if(i&&i.data&&Array.isArray(i.data)){const n=i.data.map(a=>({value:a.id||a.courseid,label:a.fullname}));t?(this.courseOptions=n,this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""):this.courseOptions=[...this.courseOptions,...n],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível nesta categoria":"Nenhum curso encontrado"}else t&&(this.courseOptions=[],this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível nesta categoria"}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},async handleCategorySelect(e){e&&(this.inputFilters.category=e.label,this.currentPage=1,await this.applyFilters(),await this.updateCourseOptionsByCategory(e,!0))},async handleCourseSelect(e){e&&(this.inputFilters.course=e.label,this.currentPage=1,await this.applyFilters())},handleOnlyActiveChange(){this.appliedFilters.onlyActive=this.inputFilters.onlyActive,this.currentPage=1,this.loadCourses()},goBack(){this.router.push({name:"listar-ofertas"})},async searchCourses(){if(this.offerId)try{this.loading=!0;const e=await ap(this.offerId,this.appliedFilters.course);if(e&&e.data&&e.data.length>0){const t=e.data.map(i=>i.id),s=await ea(this.offerId,{courseIds:t});s&&s.data.courses?this.selectedCourses=s.data.courses.map(i=>({id:i.id||i.courseid,offerCourseId:i.id,name:i.fullname,category:i.category_name||"-",turmasCount:Array.isArray(i.turmas)?i.turmas.length:0,status:i.status===1||i.status==="1"?"Ativo":"Inativo",can_delete:i.can_delete!==void 0?i.can_delete:!0,can_activate:i.can_activate!==void 0?i.can_activate:!0,turmas:Array.isArray(i.turmas)?i.turmas.map(n=>({id:n.id,nome:n.name,enrol_type:n.enrol_type||"-",vagas:n.max_users?n.max_users:"Ilimitado",inscritos:n.enrolled_users||0,dataInicio:n.start_date||"-",dataFim:n.end_date||"-"})):[]})):this.selectedCourses=[]}else this.selectedCourses=[]}catch(e){console.log(e),this.showErrorMessage("Erro ao buscar cursos. Por favor, tente novamente.")}finally{this.loading=!1}},async searchCategories(){if(this.offerId)try{this.loading=!0;const e=await Uo(this.appliedFilters.category,this.offerId);if(e&&e.data&&e.data.length>0){let t=[];for(const s of e.data){const i=s.id;if(!i)continue;const n=await mu(this.offerId,i);if(n&&n.data){const a=n.data.map(u=>({id:u.id||u.courseid,offerCourseId:u.id,name:u.fullname,category:s.name||"-",turmasCount:Array.isArray(u.turmas)?u.turmas.length:0,status:u.status===1||u.status==="1"?"Ativo":"Inativo",can_delete:u.can_delete!==void 0?u.can_delete:!0,can_activate:u.can_activate!==void 0?u.can_activate:!0,turmas:Array.isArray(u.turmas)?u.turmas.map(c=>({id:c.id,nome:c.name,enrol_type:c.enrol_type||"-",vagas:c.max_users?c.max_users:"Ilimitado",inscritos:c.enrolled_users||0,dataInicio:c.start_date||"-",dataFim:c.end_date||"-"})):[]}));t=[...t,...a]}}this.selectedCourses=t}else this.selectedCourses=[]}catch{this.showErrorMessage("Erro ao buscar categorias. Por favor, tente novamente.")}finally{this.loading=!1}},showAddCourseModal(){this.showAddCourseModalVisible=!0},async handleAddCourseConfirm(e){try{this.loading=!0;for(const t of e)await t1(this.offerId,t);await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadCourses()},handlePageChange(e){this.currentPage=e,this.loadCourses()},addTurma(e){this.selectedCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.router.push({name:"NewClass",params:{offercourseid:e.offercourseid,offerid:e.offerid||this.offerId||"0"},query:{enrol_type:e.enrolType}})},editTurma(e){const t=this.selectedCourses.find(s=>s.turmas&&s.turmas.some(i=>i.id===e.id));t?this.router.push({name:"EditClass",params:{offercourseid:t.offerCourseId,classid:e.id,offerid:this.offerId||"0"}}):this.showErrorMessage("Não foi possível editar a turma. Curso pai não encontrado.")},toggleClassStatus(e){this.selectedClass={...e,status:e.status||"Ativo"},this.showClassStatusModal=!0},async confirmToggleClassStatus(){if(this.selectedClass)try{this.loading=!0;const e=this.selectedClass.nome,t=this.selectedClass.status!=="Ativo";await m1(this.selectedClass.id,t);const s=this.selectedCourses.findIndex(i=>i.turmas.some(n=>n.id===this.selectedClass.id));if(s!==-1){const i=this.selectedCourses[s],n=i.turmas.findIndex(a=>a.id===this.selectedClass.id);n!==-1&&(i.turmas[n].status=t?"Ativo":"Inativo")}await this.loadCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedClass=null,this.showClassStatusModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}},removeTurma(e,t){const s=e.turmas[t];s.can_delete&&(this.classToDelete=s,this.classParentCourse=e,this.showDeleteClassModal=!0)},viewRegisteredUsers(e){this.router.push({name:"usuarios-matriculados",params:{offerclassid:e.id}})},duplicateTurma(e,t){this.classToDuplicate=e,this.classToDuplicateParentCourse=t,this.showDuplicateClassModal=!0},async handleDuplicateSuccess(e){await this.loadCourses(),e.duplicatedCount?this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para ${e.duplicatedCount} curso(s).`):this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para o curso "${e.targetCourseName}".`)},async confirmDeleteClass(){if(!(!this.classToDelete||!this.classParentCourse))try{this.loading=!0;const e=this.classToDelete.nome;await c1(this.classToDelete.id);const t=this.classParentCourse.turmas.findIndex(s=>s.id===this.classToDelete.id);t!==-1&&(this.classParentCourse.turmas.splice(t,1),this.classParentCourse.turmasCount=this.classParentCourse.turmas.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.classToDelete=null,this.classParentCourse=null,this.showDeleteClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}},updateTurmasCount(e){const t=this.selectedCourses.findIndex(s=>s.id===e);if(t!==-1){const s=this.selectedCourses[t];s.turmasCount=Array.isArray(s.turmas)?s.turmas.length:0,this.selectedCourses[t]={...s}}},async loadCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.appliedFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.appliedFilters.course&&(e.courseSearch=this.appliedFilters.course),this.appliedFilters.category&&(e.categorySearch=this.appliedFilters.category);const t=await ea(this.offerId,e);if(t&&t.data){let s=[],i=this.currentPage,n=1,a=0;t.data.courses&&({page:i,total_pages:n,total_items:a,courses:s}=t.data),this.currentPage=i||this.currentPage;const u=[];for(const c of s)try{const h=await gu(c.id);let m=[];h&&typeof h=="object"&&h.error===!1&&Array.isArray(h.data)&&h.data.length>0&&(m=h.data.map(p=>{let v=p.enrol_type||p.enrol||"-";return{id:p.id,nome:p.name,enrol_type:v,vagas:p.max_users?p.max_users:"Ilimitado",inscritos:p.enrolled_users||0,dataInicio:this.formatDate(p.startdate),dataFim:this.formatDate(p.enddate),status:p.status===0||p.status==="0"?"Inativo":"Ativo",can_activate:p.can_activate!==void 0?p.can_activate:!0,can_delete:p.can_delete!==void 0?p.can_delete:!0}})),u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:m.length,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:m})}catch{u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:0,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:[]})}this.selectedCourses=u,a!=null?this.totalItems=a:n>0?this.totalItems=n*this.perPage:this.totalItems=u.length,await this.loadCategoryOptions(),await this.loadCourseOptions()}else this.selectedCourses=[],this.totalItems=0,this.categoryOptions=[],this.courseOptions=[]}catch{this.showErrorMessage("Erro ao carregar cursos da oferta. Por favor, tente novamente."),this.selectedCourses=[],this.totalItems=0}finally{this.loading=!1}},async loadOffer(e){try{this.loading=!0,this.inputFilters={course:"",category:""},this.appliedFilters={course:"",category:""},this.selectedCategory=null,this.selectedCourse=null;const[t,s]=await Promise.all([rp(e),op("")]),i=Array.isArray(t)?t[0]:t;if(!i.error&&i.data){const n=i.data;if(this.offer={name:n.name,offerType:n.type,description:n.description,id:n.id,status:n.status},s&&Array.isArray(s.items)){const a=n.audiences||[];this.selectedAudiences=s.items.filter(u=>a.includes(u.id)).map(u=>({value:u.id,label:u.name.toUpperCase()}))}this.isEditing=!0,await this.loadCourses()}else throw new Error(i.message||"Erro ao carregar dados da oferta")}catch(t){this.showErrorMessage(t.message||"Erro ao carregar oferta.")}finally{this.loading=!1}},handleSelectAllAudiences(){const e=new Set(this.allAudiences.map(i=>i.value)),t=new Set(this.selectedAudiences.map(i=>i.value));let s=!0;for(const i of e)if(!t.has(i)){s=!1;break}s&&this.selectedAudiences.length===this.allAudiences.length?this.selectedAudiences=[]:this.selectedAudiences=[...this.allAudiences]},validate(){Object.keys(this.formErrors).forEach(t=>{this.formErrors[t].hasError=!1});let e=!1;return this.offer.name||(this.formErrors.name.hasError=!0,e=!0),this.selectedAudiences.length===0&&(this.formErrors.audiences.hasError=!0,e=!0),e&&this.showErrorMessage("Há campos obrigatórios a serem preenchidos."),!e},validateField(e){switch(e){case"name":this.formErrors.name.hasError=!this.offer.name;break;case"audiences":this.formErrors.audiences.hasError=this.selectedAudiences.length===0;break}return!this.formErrors[e].hasError},async saveOffer(){var e;if(this.loading=!0,!this.validate()){this.loading=!1;return}try{const t={name:this.offer.name,description:this.offer.description,type:this.offer.offerType,status:this.offer.status,audiences:this.selectedAudiences.map(i=>i.value)};let s;if(this.isEditing&&this.offerId)if(t.id=this.offerId,s=await np(t),s&&!s.error)if(await this.updateOfferAudiences())this.showSuccessMessage("Oferta atualizada com sucesso!");else throw new Error("Oferta atualizada, mas houve falha ao atualizar públicos-alvo.");else{const i=(s==null?void 0:s.message)||(s==null?void 0:s.error)||"Falha ao atualizar oferta.";throw new Error(i)}else if(s=await np(t),s&&s.data&&s.data.id){const i=s.data.id;this.offerId=i,this.offer.id=i,this.isEditing=!0,this.showSuccessMessage("Oferta salva com sucesso!");const n=`/edit-offer/${this.offerId}`;this.router.replace(n)}else{const i=(s==null?void 0:s.message)||((e=s==null?void 0:s[0])==null?void 0:e.message)||"Falha ao salvar a oferta.";throw new Error(i)}}catch(t){this.showErrorMessage(t.message||"Erro ao salvar oferta. Verifique os dados e tente novamente.")}finally{this.loading=!1}},async updateOfferAudiences(){if(!this.offerId)return!1;try{const e=this.selectedAudiences.map(s=>s.value),t=await n1(this.offerId,e);return!!(t&&!t.error)}catch{return this.showErrorMessage("Erro ao atualizar públicos-alvo."),!1}finally{}},toggleCourseStatus(e){e.can_activate&&(this.selectedCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status==="Ativo"?e.can_activate?"Inativar":"Não é possível inativar este curso":e.can_activate?"Ativar":"Não é possível ativar este curso"},async confirmToggleCourseStatus(){if(this.selectedCourse)try{this.loading=!0;const e=this.selectedCourse.status!=="Ativo",t=this.selectedCourse.name,s=this.selectedCourse.offerCourseId||this.selectedCourse.id;await r1(this.offerId,s,e);const i=this.selectedCourses.findIndex(n=>n.id===this.selectedCourse.id);i!==-1&&(this.selectedCourses[i].status=e?"Ativo":"Inativo"),this.showCourseStatusModal=!1,this.selectedCourse=null,await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status do curso.")}finally{this.loading=!1}},deleteCourse(e){e.can_delete&&(this.courseToDelete=e,this.showDeleteCourseModal=!0)},async confirmDeleteCourse(){if(this.courseToDelete)try{this.loading=!0;const e=this.courseToDelete.name,t=this.courseToDelete.offerCourseId||this.courseToDelete.id;await s1(this.offerId,t),this.selectedCourses=this.selectedCourses.filter(s=>s.id!==this.courseToDelete.id),await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(`Curso "${e}" excluído com sucesso.`),this.courseToDelete=null,this.showDeleteCourseModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}},async created(){try{const t=await pu();if(t&&t.data){const{enabled:s,types:i,default:n}=t.data;this.typeOptionsEnabled=s,s&&Array.isArray(i)&&(this.offerTypeOptions=i.map(a=>({value:a,label:a})),n&&!this.isEditing&&(this.offer.offerType=n))}}catch(t){this.showErrorMessage(t.message||"Erro ao carregar opções de tipos.")}const e=this.route.params.id;e&&(this.offerId=parseInt(e),this.loadOffer(this.offerId))},watch:{"inputFilters.course"(e,t){e.length===0&&t.length>0&&this.loadCourses()},"inputFilters.category"(e,t){e.length===0&&t.length>0&&this.loadCourses()},selectedCategory(e){e||(this.inputFilters.category="",this.appliedFilters.category&&(this.appliedFilters.category="",this.loadCourses()),this.loadCourseOptions())},selectedCourse(e){e||(this.inputFilters.course="",this.appliedFilters.course&&(this.appliedFilters.course="",this.loadCourses()))},currentPage(){this.loadCourses()},perPage(){this.currentPage=1,this.loadCourses()}}},oO={id:"new-offer-component",class:"new-offer"},iO={key:0,class:"alert alert-warning"},aO={class:"section-container"},lO={class:"form-row mb-3"},uO={class:"form-group"},cO={class:"input-container"},dO={key:0,class:"form-group"},fO={class:"input-container"},hO={class:"form-row mb-3",style:{"margin-bottom":"1.5rem"}},pO={class:"form-group"},mO={class:"label-container"},gO={class:"label-with-help"},vO={class:"input-container"},_O={class:"form-group text-editor-container"},yO={class:"limited-width-editor"},bO={key:0,class:"section-title"},wO={key:1,class:"message-container"},EO={key:2},CO={class:"filters-left-group"},DO={class:"filters-right-group"},xO={class:"empty-state"},SO={class:"no-results"},OO=["title"],IO={class:"action-buttons"},NO=["onClick"],TO=["src"],AO=["onClick","disabled","title"],MO=["onClick","disabled","title"],PO={class:"turmas-container"},kO={class:"turmas-content"},RO={key:0},VO={class:"turma-col"},FO=["title"],UO={class:"turma-col"},LO={class:"turma-col"},BO={class:"turma-col"},$O={class:"turma-col"},jO={class:"turma-col"},HO={class:"turma-col"},qO={class:"action-buttons"},zO=["onClick"],WO=["src"],GO=["onClick"],KO=["onClick"],YO=["title","onClick"],QO=["onClick","disabled","title"],ZO={key:1,class:"empty-turmas"},JO={class:"d-flex justify-content-between align-items-center"},XO={class:"actions-container offer-actions"};function eI(e,t,s,i,n,a){var ke,ae,We,J,$e,ut,Oe,Ee,Ut,Xt,yt;const u=X("BackButton"),c=X("PageHeader"),h=X("CustomInput"),m=X("CustomSelect"),p=X("HelpIcon"),v=X("Autocomplete"),w=X("TextEditor"),D=X("FilterGroup"),k=X("CustomCheckbox"),U=X("FilterTag"),te=X("FilterTags"),T=X("FilterRow"),se=X("CollapsibleTable"),G=X("Pagination"),we=X("CustomButton"),Q=X("AddCourseModal"),he=X("ConfirmationModal"),be=X("DuplicateClassModal"),Ae=X("EnrolTypeModal"),le=X("LFLoading"),ie=X("Toast");return O(),N("div",oO,[A(c,{title:n.isEditing?`Editar oferta: ${n.offer.name}`:"Nova oferta"},{actions:ye(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),n.showWarning?(O(),N("div",iO,t[21]||(t[21]=[f("i",{class:"fas fa-exclamation-triangle"},null,-1),tt(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância da oferta. ")]))):ce("",!0),f("div",aO,[t[27]||(t[27]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",lO,[f("div",uO,[t[22]||(t[22]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Nome da Oferta"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),f("div",cO,[A(h,{modelValue:n.offer.name,"onUpdate:modelValue":t[0]||(t[0]=de=>n.offer.name=de),placeholder:"Oferta 0001",width:280,required:"","has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=de=>a.validateField("name"))},null,8,["modelValue","has-error","error-message"])])]),n.typeOptionsEnabled?(O(),N("div",dO,[t[23]||(t[23]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Tipo da oferta")])],-1)),f("div",fO,[A(m,{modelValue:n.offer.offerType,"onUpdate:modelValue":t[2]||(t[2]=de=>n.offer.offerType=de),options:n.offerTypeOptions,width:280},null,8,["modelValue","options"])])])):ce("",!0)]),f("div",hO,[f("div",pO,[f("div",mO,[f("div",gO,[t[24]||(t[24]=f("label",{class:"form-label"},"Público-alvo",-1)),t[25]||(t[25]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(p,{title:"Ajuda com público-alvo",text:"Selecione pelo menos um público-alvo para a oferta."})])]),f("div",vO,[A(v,{class:"autocomplete-audiences",modelValue:n.selectedAudiences,"onUpdate:modelValue":[t[3]||(t[3]=de=>n.selectedAudiences=de),t[4]||(t[4]=de=>a.validateField("audiences"))],items:n.audienceTypeOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message,onSelectAll:a.handleSelectAllAudiences},null,8,["modelValue","items","has-error","error-message","onSelectAll"])])])]),f("div",_O,[t[26]||(t[26]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Descrição da oferta")])],-1)),f("div",yO,[A(w,{modelValue:n.offer.description,"onUpdate:modelValue":t[5]||(t[5]=de=>n.offer.description=de),placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])])]),f("div",{class:pe(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(O(),N("h2",bO,"CURSOS")):ce("",!0),!a.canManageCourses||!n.isEditing?(O(),N("div",wO,t[28]||(t[28]=[f("div",{class:"lock-message"},[f("i",{class:"fas fa-lock lock-icon"}),f("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))):ce("",!0),n.isEditing&&a.canManageCourses?(O(),N("div",EO,[A(T,{inline:"",class:"courses-filter-row"},{default:ye(()=>[f("div",CO,[A(D,null,{default:ye(()=>[A(v,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[6]||(t[6]=de=>n.selectedCategory=de),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","no-results-text","onSelect"])]),_:1}),A(D,null,{default:ye(()=>[A(v,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[7]||(t[7]=de=>n.selectedCourse=de),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onSelect","onLoadMore","onSearch"])]),_:1}),A(D,{isCheckbox:!0,class:"checkbox-filter-group"},{default:ye(()=>[A(k,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[8]||(t[8]=de=>n.inputFilters.onlyActive=de),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),n.appliedFilters.course?(O(),Pt(te,{key:0,class:"mt-3"},{default:ye(()=>[A(U,{onRemove:t[9]||(t[9]=de=>a.removeFilter("course"))},{default:ye(()=>[tt(" Curso: "+W(n.appliedFilters.course),1)]),_:1})]),_:1})):ce("",!0)]),f("div",DO,[f("button",{class:"btn btn-primary",onClick:t[10]||(t[10]=(...de)=>a.showAddCourseModal&&a.showAddCourseModal(...de))}," Adicionar curso ")])]),_:1}),A(se,{headers:n.courseTableHeaders,items:n.selectedCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":ye(()=>[f("div",xO,[f("span",SO,W(n.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":ye(({item:de})=>[f("span",{title:de.name},W(de.name.length>50?de.name.slice(0,50)+"...":de.name),9,OO)]),"item-actions":ye(({item:de})=>[f("div",IO,[f("button",{class:"btn-action btn-add",onClick:Ke=>a.addTurma(de),title:"Adicionar turma"},[f("img",{src:n.icons.plus,alt:"Adicionar turma"},null,8,TO)],8,NO),f("button",{class:pe(["btn-action",de.status==="Ativo"?"btn-deactivate":"btn-activate"]),onClick:Ke=>a.toggleCourseStatus(de),disabled:de.status==="Inativo"&&!de.can_activate||!de.can_activate,title:a.getStatusButtonTitle(de)},[f("i",{class:pe(de.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,AO),f("button",{class:"btn-action btn-delete",onClick:Ke=>a.deleteCourse(de),disabled:!de.can_delete,title:de.can_delete?"Excluir":"Não é possível excluir este curso"},t[29]||(t[29]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,MO)])]),"expanded-content":ye(({item:de})=>[f("div",PO,[t[34]||(t[34]=f("div",{class:"turmas-header"},[f("div",{class:"turma-col"},"NOME DA TURMA"),f("div",{class:"turma-col"},"TIPO DE INSCRIÇÃO"),f("div",{class:"turma-col"},"QTD. DE VAGAS"),f("div",{class:"turma-col"},"QTD. DE ALUNOS INSCRITOS"),f("div",{class:"turma-col"},"DATA INÍCIO DA TURMA"),f("div",{class:"turma-col"},"DATA FIM DA TURMA"),f("div",{class:"turma-col"},"AÇÕES")],-1)),f("div",kO,[de.turmas&&de.turmas.length>0?(O(),N("div",RO,[(O(!0),N(Me,null,mt(de.turmas,(Ke,fs)=>(O(),N("div",{class:"turmas-row",key:fs},[f("div",VO,[f("span",{title:Ke.nome},W(Ke.nome.length>20?Ke.nome.slice(0,20)+"...":Ke.nome),9,FO)]),f("div",UO,W(a.getEnrolTypeLabel(Ke.enrol_type)),1),f("div",LO,W(Ke.vagas),1),f("div",BO,W(Ke.inscritos),1),f("div",$O,W(Ke.dataInicio),1),f("div",jO,W(Ke.dataFim),1),f("div",HO,[f("div",qO,[f("button",{class:"btn-action btn-users",onClick:zt=>a.viewRegisteredUsers(Ke),title:"Usuários Matriculados"},[f("img",{src:n.icons.users,alt:"Usuários Matriculados"},null,8,WO)],8,zO),f("button",{class:"btn-action btn-edit",onClick:zt=>a.editTurma(Ke),title:"Editar"},t[30]||(t[30]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,GO),f("button",{class:"btn-action btn-duplicate",onClick:zt=>a.duplicateTurma(Ke,de),title:"Duplicar Turma"},t[31]||(t[31]=[f("i",{class:"fas fa-copy"},null,-1)]),8,KO),f("button",{class:pe(["btn-action",Ke.status==="Ativo"?"btn-deactivate":"btn-activate"]),title:Ke.status==="Ativo"?"Inativar":"Ativar",onClick:zt=>a.toggleClassStatus(Ke)},[f("i",{class:pe(Ke.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,YO),f("button",{class:"btn-action btn-delete",onClick:zt=>a.removeTurma(de,fs),disabled:!Ke.can_delete,title:Ke.can_delete?"Excluir":"Não é possível excluir esta turma"},t[32]||(t[32]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,QO)])])]))),128))])):(O(),N("div",ZO,t[33]||(t[33]=[f("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),A(G,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[11]||(t[11]=de=>n.currentPage=de),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[12]||(t[12]=de=>n.perPage=de),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"])])):ce("",!0)],2),t[36]||(t[36]=f("hr",null,null,-1)),f("div",JO,[t[35]||(t[35]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[tt(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",XO,[A(we,{variant:"primary",label:"Salvar",onClick:a.saveOffer},null,8,["onClick"]),A(we,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),A(Q,{modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[13]||(t[13]=de=>n.showAddCourseModalVisible=de),"offer-id":n.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offer-id","onConfirm"]),A(he,{show:n.showCourseStatusModal,title:((ke=n.selectedCourse)==null?void 0:ke.status)==="Ativo"?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((ae=n.selectedCourse)==null?void 0:ae.status)==="Ativo"?"":"Tem certeza que deseja ativar este curso?","list-items":((We=n.selectedCourse)==null?void 0:We.status)==="Ativo"?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((J=n.selectedCourse)==null?void 0:J.status)==="Ativo"?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:(($e=n.selectedCourse)==null?void 0:$e.status)==="Ativo"?"warning":"question",onClose:t[14]||(t[14]=de=>n.showCourseStatusModal=!1),onConfirm:a.confirmToggleCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),A(he,{show:n.showDeleteCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[15]||(t[15]=de=>n.showDeleteCourseModal=!1),onConfirm:a.confirmDeleteCourse},null,8,["show","onConfirm"]),A(he,{show:n.showDeleteClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[16]||(t[16]=de=>n.showDeleteClassModal=!1),onConfirm:a.confirmDeleteClass},null,8,["show","onConfirm"]),A(he,{show:n.showClassStatusModal,title:((ut=n.selectedClass)==null?void 0:ut.status)==="Ativo"?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:((Oe=n.selectedClass)==null?void 0:Oe.status)==="Ativo"?"":"Tem certeza que deseja ativar esta turma?","list-items":((Ee=n.selectedClass)==null?void 0:Ee.status)==="Ativo"?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":((Ut=n.selectedClass)==null?void 0:Ut.status)==="Ativo"?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:((Xt=n.selectedClass)==null?void 0:Xt.status)==="Ativo"?"warning":"question",onClose:t[17]||(t[17]=de=>n.showClassStatusModal=!1),onConfirm:a.confirmToggleClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),A(be,{show:n.showDuplicateClassModal,turma:n.classToDuplicate,parentCourse:n.classToDuplicateParentCourse,offerId:n.offerId,onClose:t[18]||(t[18]=de=>n.showDuplicateClassModal=!1),onSuccess:a.handleDuplicateSuccess,onLoading:t[19]||(t[19]=de=>n.loading=de),onError:a.showErrorMessage},null,8,["show","turma","parentCourse","offerId","onSuccess","onError"]),A(Ae,{show:n.showEnrolTypeModal,offercourseid:(yt=n.selectedCourseForClass)==null?void 0:yt.offerCourseId,offerid:n.offerId||"0",onClose:t[20]||(t[20]=de=>n.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offercourseid","offerid","onConfirm"]),A(le,{"is-loading":n.loading},null,8,["is-loading"]),A(ie,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const fp=ze(nO,[["render",eI],["__scopeId","data-v-e21444bf"]]),VV="",tI={name:"NewClassView",components:{CustomInput:Lo,CustomSelect:mr,CustomButton:Bn,PageHeader:oa,BackButton:wu,Autocomplete:jo,TextEditor:dp,CustomCheckbox:sa,FilterRow:ra,FilterGroup:na,Toast:Bo,HelpIcon:Eu,FilterTag:$o,FilterTags:ia},setup(){const e=Ji(),t=ep();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},props:{offercourseid:{type:[Number,String],required:!0},classid:{type:[Number,String],required:!1,default:null},offerid:{type:[Number,String],required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,classId:null,enrolmentMethods:[],classData:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],situationOptions:[],extensionSituationOptions:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(!this.offercourseid)throw new Error("offercourseid não foi definido. Isso causará problemas ao salvar a turma.");if(this.classData.offercourseid=parseInt(this.offercourseid),this.classid)this.isEditing=!0,this.classId=parseInt(this.classid);else if(this.route.query.classid&&this.route.query.edit==="true"){this.isEditing=!0,this.classId=parseInt(this.route.query.classid);const e=this.route.query.offerid||this.offerid;this.$nextTick(()=>{this.router.replace({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classId,offerid:e||"0"}})})}await this.loadInitialData(),this.isEditing&&this.classId?(await this.loadClassData(),this.syncSituations(),this.$nextTick(()=>{this.restartComponent()})):this.syncSituations()},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},computed:{maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.classData.startdate&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.classData.startdate===this.classData.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."))},"classData.startdate":function(){this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enableenddate":function(e){e&&this.classData.optional_fields.enddate&&this.validateField("enddate"),!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enrolperiod":function(e){this.classData.optional_fields.enableenrolperiod&&e&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enableenrolperiod=!1,this.classData.optional_fields.enrolperiod=null,this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},syncSituations(){this.extensionSituations=this.classData.optional_fields.extensionallowedsituations.map(e=>{const t=parseInt(e);return t===0?{value:t,label:"Inscrito"}:this.extensionSituationOptions.find(s=>s.value===t)||{value:t,label:`Situação ${e}`}}),this.reenrolSituations=this.classData.optional_fields.reenrolmentsituations.map(e=>this.situationOptions.find(t=>t.value===parseInt(e))||{value:parseInt(e),label:`Situação ${e}`})},async loadInitialData(){try{this.loading=!0,await this.loadOfferCourse(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados. Valores padrão serão usados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await l1(this.offercourseid);!e.error&&e.data&&(this.offerCourse=e.data)}catch{this.offerCourse={id:this.offercourseid,offerid:null}}},async loadRoles(){const e=await ta(this.offercourseid);let t=e==null?void 0:e.data;if(!e.error&&t){if(this.roleOptions=t.map(s=>({value:s.id,label:s.name})),!this.classData.optional_fields.roleid){const s=this.roleOptions.find(i=>i.value===5);this.classData.optional_fields.roleid=(s==null?void 0:s.value)??this.roleOptions[0].value}return}},async loadSituations(){const e=await f1();if(Array.isArray(e)){const t=[6,7,8,4,5],s=e.filter(a=>t.includes(a.id));this.situationOptions=s.map(a=>({value:a.id,label:a.name.charAt(0).toUpperCase()+a.name.slice(1).toLowerCase()}));const i=[0,1],n=e.filter(a=>i.includes(a.id));this.extensionSituationOptions=n.map(a=>({value:a.id,label:a.name}))}},async loadEnrolmentMethods(){const e=this.route.query.enrol_type;!this.isEditing&&e&&(this.classData.enrol=e);const t=await lp(!0);t&&Array.isArray(t)&&(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})))},validate(){Object.keys(this.formErrors).forEach(s=>{this.formErrors[s].hasError=!1}),this.validationAlert.show=!1;let e=!1;return this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate?this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate)?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0,e=!0):this.formErrors.startdate.hasError=!1:(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&(this.classData.optional_fields.enddate?this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate)?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0,e=!0):this.formErrors.enddate.hasError=!1:(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0,e=!0)),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0)),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":const s=this.classData.startdate,i=s&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate);s?i?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":const n=this.classData.optional_fields.enableenddate,a=this.classData.optional_fields.enddate,u=n&&a&&this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate);n&&!a?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):u?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const c=this.classData.optional_fields.enableenrolperiod,h=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",m=this.maxEnrolPeriod!==null&&h&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;c&&!h?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):c&&m?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,n=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},async loadClassData(){this.loading=!0;const e=await Lr(this.classId);if(e.error==!0)throw new Error(e.exception);this.classData=e.data,e.data.optional_fields&&this.processOptionalFields(e.data.optional_fields),e.data.teachers&&Array.isArray(e.data.teachers)&&(this.selectedTeachers=e.data.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`,this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationOptions.find(i=>i.value===parseInt(t))||{value:parseInt(t),label:`Situação ${t}`}))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>{const s=parseInt(t);return s===0?{value:s,label:"Inscrito"}:s===1?this.extensionSituationOptions.find(n=>n.value===s)||{value:s,label:"Em andamento"}:{value:s,label:`Situação ${t}`}}))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(i=>i.id||i.value)??[];const s=await d1(this.offercourseid,this.classId,e,t);this.teacherList=!s.error&&s.data?s.data:[]},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(!this.validate())return;this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));e.teachers=this.selectedTeachers.map(n=>n.id),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(n=>n.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(n=>n.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(n=>{const a=e.optional_fields[n];(a===0||a===null||a===""||a===void 0)&&(e.optional_fields[n]=void 0)}),this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(n=>!e[n]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}if(e.offercourseid=parseInt(e.offercourseid),this.isEditing&&this.classId){e.offerclassid=this.classId;let n=await u1(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.loadClassData()):this.showErrorMessage(n.exception.message)}else{let n=await a1(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.classId=n.data.offerclassid,this.isEditing=!0,this.router.push({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classId,offerid:this.offerid}})):this.showErrorMessage(n.exception.message)}this.loading=!1},goBack(){if(this.offerid)this.router.push({name:"editar-oferta",params:{id:this.offerid}});else if(this.offerCourse&&this.offerCourse.offerid)this.router.push({name:"editar-oferta",params:{id:this.offerCourse.offerid}});else{const e=this.route.query.offerid;e?this.router.push({name:"editar-oferta",params:{id:e}}):this.router.push({name:"listar-ofertas"})}},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){this.extensionSituationOptions.every(t=>this.extensionSituations.some(s=>s.value===t.value))?this.extensionSituations=[]:this.extensionSituations=[...this.extensionSituationOptions],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){this.situationOptions.every(t=>this.reenrolSituations.some(s=>s.value===t.value))?this.reenrolSituations=[]:this.reenrolSituations=[...this.situationOptions],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},sI={class:"new-class",ref:"classView"},rI={class:"page-header-container"},nI={key:0,class:"validation-alert"},oI={class:"section-container"},iI={class:"form-group mb-3"},aI={class:"label-with-help"},lI={class:"limited-width-input",style:{"max-width":"280px"}},uI={class:"form-row mb-3"},cI={class:"form-group"},dI={class:"label-with-help"},fI={class:"limited-width-input"},hI={class:"label-with-help"},pI={class:"input-with-checkbox"},mI={class:"limited-width-input"},gI={class:"form-row mb-3"},vI={class:"label-with-help"},_I={class:"label-with-help"},yI={key:2,class:"form-group"},bI={class:"form-group mb-3"},wI={class:"label-with-help"},EI={class:"limited-width-editor"},CI={class:"form-row mb-3"},DI={key:0,class:"form-group"},xI={class:"label-with-help"},SI={class:"limited-width-input"},OI={key:1,class:"form-group"},II={class:"label-with-help"},NI={class:"limited-width-input"},TI={class:"form-group"},AI={class:"label-with-help"},MI={class:"limited-width-input"},PI={class:"form-row mb-3"},kI={class:"label-with-help"},RI={class:"input-with-checkbox"},VI={class:"limited-width-input"},FI={class:"section-container"},UI={class:"form-row mb-3"},LI={class:"label-with-help"},BI={class:"form-row mb-3"},$I={class:"limited-width-input"},jI={class:"form-row mb-3"},HI={class:"limited-width-input"},qI={class:"form-row mb-3"},zI={class:"limited-width-input"},WI={class:"limited-width-select"},GI={key:1,class:"section-container"},KI={class:"form-row mb-3"},YI={class:"form-group"},QI={class:"label-with-help"},ZI={class:"limited-width-select"},JI={class:"section-container"},XI={class:"form-group mb-3"},eN={class:"label-with-help"},tN={class:"limited-width-select"},sN={class:"position-relative",ref:"teacherSearchContainer"},rN={class:"input-wrapper with-icon"},nN={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},oN=["onClick","onMouseenter"],iN={key:0,class:"text-muted small"},aN={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},lN={class:"my-4"},uN=["onClick"],cN={class:"actions-container"},dN={key:2,class:"loading"};function fN(e,t,s,i,n,a){const u=X("BackButton"),c=X("PageHeader"),h=X("HelpIcon"),m=X("CustomInput"),p=X("CustomCheckbox"),v=X("TextEditor"),w=X("CustomSelect"),D=X("Autocomplete"),k=X("CustomButton"),U=X("Toast"),te=__("tooltip");return O(),N("div",sI,[f("div",rI,[A(c,{title:n.isEditing?"Editar turma":"Nova turma"},{actions:ye(()=>[A(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),n.validationAlert.show?(O(),N("div",nI,[t[34]||(t[34]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),f("span",null,W(n.validationAlert.message),1)])):ce("",!0),f("div",oI,[t[51]||(t[51]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",iI,[f("div",aI,[t[35]||(t[35]=f("label",{class:"form-label"},"Nome da turma",-1)),t[36]||(t[36]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(h,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),f("div",lI,[A(m,{modelValue:n.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=T=>n.classData.classname=T),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=T=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),f("div",uI,[f("div",cI,[f("div",dI,[t[37]||(t[37]=f("label",{class:"form-label"},"Data início da turma",-1)),t[38]||(t[38]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(h,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 16/03/2025."})]),f("div",fI,[A(m,{modelValue:n.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=T=>n.classData.startdate=T),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=T=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),f("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableenddate}])},[f("div",hI,[t[39]||(t[39]=f("label",{class:"form-label"},"Data fim da turma",-1)),t[40]||(t[40]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(h,{title:"Ajuda com data fim da turma",text:"Insira uma data fim para a turma. Exemplo: 16/12/2025."})]),f("div",pI,[f("div",mI,[A(m,{modelValue:n.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=T=>n.classData.optional_fields.enddate=T),type:"date",width:180,disabled:!n.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=T=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),A(p,{modelValue:n.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=T=>n.classData.optional_fields.enableenddate=T),id:"enableEndDate",label:"Habilitar data fim da turma",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),f("div",gI,[n.classData.enrol=="offer_self"?(O(),N("div",{key:0,class:pe(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[f("div",vI,[t[41]||(t[41]=f("label",{class:"form-label"},"Data início pré-inscrição",-1)),A(h,{title:"Ajuda com data início pré-inscrição",text:"Data de início do período de pré-inscrição."})]),A(m,{modelValue:n.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=T=>n.classData.optional_fields.preenrolmentstartdate=T),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=T=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ce("",!0),n.classData.enrol=="offer_self"?(O(),N("div",{key:1,class:pe(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[f("div",_I,[t[42]||(t[42]=f("label",{class:"form-label"},"Data fim pré-inscrição",-1)),A(h,{title:"Ajuda com data fim pré-inscrição",text:"Data de término do período de pré-inscrição."})]),A(m,{modelValue:n.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=T=>n.classData.optional_fields.preenrolmentenddate=T),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=T=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ce("",!0),n.classData.enrol=="offer_self"?(O(),N("div",yI,[t[43]||(t[43]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"}," ")],-1)),A(p,{modelValue:n.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=T=>n.classData.optional_fields.enablepreenrolment=T),id:"enablePreEnrolment",label:"Habilitar pré-inscrição",disabled:!1},null,8,["modelValue"])])):ce("",!0)]),f("div",bI,[f("div",wI,[t[44]||(t[44]=f("label",{class:"form-label"},"Descrição da turma",-1)),A(h,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),f("div",EI,[A(v,{modelValue:n.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=T=>n.classData.optional_fields.description=T),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),f("div",CI,[n.classData.enrol=="offer_self"?(O(),N("div",DI,[f("div",xI,[t[45]||(t[45]=f("label",{class:"form-label"},"Mínimo de usuários inscritos",-1)),A(h,{title:"Ajuda com mínimo de usuários",text:"Número mínimo de usuários para a turma."})]),f("div",SI,[A(m,{modelValue:n.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=T=>n.classData.optional_fields.minusers=T),type:"number",width:180},null,8,["modelValue"])])])):ce("",!0),n.classData.enrol=="offer_self"?(O(),N("div",OI,[f("div",II,[t[46]||(t[46]=f("label",{class:"form-label"},"Máximo de usuários inscritos",-1)),A(h,{title:"Ajuda com máximo de usuários",text:"Número máximo de usuários para a turma."})]),f("div",NI,[A(m,{modelValue:n.classData.optional_fields.maxusers,"onUpdate:modelValue":t[14]||(t[14]=T=>n.classData.optional_fields.maxusers=T),type:"number",width:180},null,8,["modelValue"])])])):ce("",!0),f("div",TI,[f("div",AI,[t[47]||(t[47]=f("label",{class:"form-label"},"Papel atribuído por padrão",-1)),t[48]||(t[48]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(h,{title:"Ajuda com papel atribuído por padrão",text:"O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…"})]),f("div",MI,[A(w,{modelValue:n.classData.optional_fields.roleid,"onUpdate:modelValue":t[15]||(t[15]=T=>n.classData.optional_fields.roleid=T),options:n.roleOptions,width:180,required:"","has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[16]||(t[16]=T=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),f("div",PI,[f("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod}])},[f("div",kI,[t[49]||(t[49]=f("label",{class:"form-label"},"Prazo de conclusão da turma",-1)),t[50]||(t[50]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),A(h,{title:"Ajuda com prazo de conclusão da turma",text:"O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula."+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),f("div",RI,[f("div",VI,[A(m,{modelValue:n.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[17]||(t[17]=T=>n.classData.optional_fields.enrolperiod=T),type:"number",width:180,disabled:!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[18]||(t[18]=T=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),vt(A(p,{modelValue:n.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[19]||(t[19]=T=>n.classData.optional_fields.enableenrolperiod=T),id:"enableEnrolPeriod",label:"Habilitar prazo de conclusão",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[te,a.shouldDisableEnrolPeriod?"Não é possível habilitar prazo de conclusão para turmas de um dia (data início = data fim)":""]])])],2)])]),f("div",FI,[f("div",UI,[f("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[f("div",LI,[t[52]||(t[52]=f("label",{class:"form-label"},"Prorrogação de matrícula",-1)),A(h,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),vt(A(p,{modelValue:n.classData.optional_fields.enableextension,"onUpdate:modelValue":t[20]||(t[20]=T=>n.classData.optional_fields.enableextension=T),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!n.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[te,n.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),f("div",BI,[f("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[53]||(t[53]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias serão acrescentados para prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",$I,[A(m,{modelValue:n.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[21]||(t[21]=T=>n.classData.optional_fields.extensionperiod=T),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[22]||(t[22]=T=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",jI,[f("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[54]||(t[54]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",HI,[A(m,{modelValue:n.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[23]||(t[23]=T=>n.classData.optional_fields.extensiondaysavailable=T),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[24]||(t[24]=T=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",qI,[f("div",{class:pe(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[55]||(t[55]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantas vezes o usuário pode pedir prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",zI,[A(m,{modelValue:n.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[25]||(t[25]=T=>n.classData.optional_fields.extensionmaxrequests=T),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[26]||(t[26]=T=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",{class:pe(["form-group mb-3",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[56]||(t[56]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),f("div",WI,[A(D,{modelValue:n.extensionSituations,"onUpdate:modelValue":t[27]||(t[27]=T=>n.extensionSituations=T),items:n.extensionSituationOptions,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)]),n.classData.enrol=="offer_self"?(O(),N("div",GI,[f("div",KI,[f("div",YI,[f("div",QI,[t[57]||(t[57]=f("label",{class:"form-label"},"Habilitar rematrícula",-1)),A(h,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),A(p,{modelValue:n.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[28]||(t[28]=T=>n.classData.optional_fields.enablereenrol=T),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),f("div",{class:pe(["form-group mb-3",{disabled:!n.classData.optional_fields.enablereenrol}])},[t[58]||(t[58]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),f("div",ZI,[A(D,{modelValue:n.reenrolSituations,"onUpdate:modelValue":t[29]||(t[29]=T=>n.reenrolSituations=T),items:n.situationOptions,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):ce("",!0),f("div",JI,[f("div",XI,[f("div",eN,[t[59]||(t[59]=f("label",{class:"form-label"},"Atribuir corpo docente",-1)),A(h,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),f("div",tN,[f("div",sN,[f("div",rN,[vt(f("input",{type:"text","onUpdate:modelValue":t[30]||(t[30]=T=>n.teacherSearchTerm=T),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[31]||(t[31]=(...T)=>a.handleTeacherInput&&a.handleTeacherInput(...T)),onFocus:t[32]||(t[32]=(...T)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...T)),onKeydown:t[33]||(t[33]=(...T)=>a.handleKeydown&&a.handleKeydown(...T)),ref:"teacherSearchInput"},null,544),[[ws,n.teacherSearchTerm]])]),n.showTeacherDropdown&&n.teacherList.length>0?(O(),N("div",nN,[(O(!0),N(Me,null,mt(n.teacherList,(T,se)=>(O(),N("div",{key:T.id,class:pe(["dropdown-item",{active:n.highlightedIndex===se}]),onClick:G=>a.selectTeacher(T),onMouseenter:G=>n.highlightedIndex=se},[f("div",null,[f("div",null,W(T.fullname),1),T.email?(O(),N("div",iN,W(T.email),1)):ce("",!0)])],42,oN))),128))],512)):ce("",!0),n.showTeacherDropdown&&n.teacherSearchTerm.length>=3&&n.teacherList.length===0?(O(),N("div",aN,t[60]||(t[60]=[f("div",{class:"dropdown-item-text text-center fst-italic"},"Nenhum professor encontrado",-1)]))):ce("",!0)],512),f("div",lN,[(O(!0),N(Me,null,mt(n.selectedTeachers,(T,se)=>(O(),N("a",{key:T.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:G=>a.removeTeacher(T.id)},[t[61]||(t[61]=f("i",{class:"fas fa-times mr-1"},null,-1)),tt(" "+W(T.fullname),1)],8,uN))),128))])])])]),t[63]||(t[63]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[tt(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",cN,[A(k,{variant:"primary",label:"Salvar",loading:n.loading,onClick:a.saveClass},null,8,["loading","onClick"]),A(k,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),n.loading?(O(),N("div",dN,t[62]||(t[62]=[f("div",{class:"spinner-border",role:"status"},[f("span",{class:"sr-only"},"Carregando...")],-1)]))):ce("",!0),A(U,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],512)}const hp=ze(tI,[["render",fN],["__scopeId","data-v-c33e8dc0"]]),hN=[{path:"/",name:"listar-ofertas",component:Jw,meta:{title:"Gerenciar Ofertas"}},{path:"/new-offer",name:"nova-oferta",component:fp,meta:{title:"Nova Oferta"}},{path:"/edit-offer/:id",name:"editar-oferta",component:fp,props:!0,meta:{title:"Editar Oferta"}},{path:"/new-class/:offercourseid/:offerid",name:"NewClass",component:hp,props:!0,meta:{title:"Nova Turma"}},{path:"/edit-class/:offercourseid/:classid/:offerid",name:"EditClass",component:hp,props:!0,meta:{title:"Editar Turma"}},{path:"/new-subscribed-users/:offerclassid",name:"usuarios-matriculados",component:Rx,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],Ho=Z0({history:l0("/local/offermanager/"),routes:hN,scrollBehavior(){return{top:0}}});Ho.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),Ho.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&Ho.push("/")});const FV="",pN=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{pN();const s=Qy(Mb);if(s.use(Ib()),s.use(Ho),t&&t.route){let n={"new-offer":"/new-offer","edit-offer":"/edit-offer","new-subscribed-users":"/new-subscribed-users","new-class":"/new-class","edit-class":"/edit-class"}[t.route]||"/";t.route==="edit-offer"&&t.offerId&&(n=`/edit-offer/${t.offerId}`),t.route==="new-subscribed-users"&&t.subscribeId&&(n=`/new-subscribed-users/${t.subscribeId}`),t.route==="new-class"&&t.offercourseid&&(t.offerid?n=`/new-class/${t.offercourseid}/${t.offerid}`:n=`/new-class/${t.offercourseid}/0`),t.route==="edit-class"&&t.offercourseid&&t.classid&&(t.offerid?n=`/edit-class/${t.offercourseid}/${t.classid}/${t.offerid}`:n=`/edit-class/${t.offercourseid}/${t.classid}/0`),Ho.replace(n)}return s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
