<?php

defined('MOODLE_INTERNAL') || die();

function local_catalog_add_menubar_icon(){
	global $PAGE, $USER;

	if(!get_config("local_catalog", "enableplugin") || isguestuser()){
		return false;
	}

	$is_admin_revvo = is_siteadmin($USER->id);
	$is_admin_cliente = false;
	$is_admin_sebrae = false;

	if (function_exists('tool_lfxp_is_client')) {
		$is_admin_cliente = tool_lfxp_is_client($USER->id);
	} else {
		$roles = get_user_roles(\context_system::instance(), $USER->id, false);
		$shortnames = array_column($roles, 'shortname');
		$is_admin_cliente = in_array('client', $shortnames);
	}

	$roles = get_user_roles(\context_system::instance(), $USER->id, false);
	$shortnames = array_column($roles, 'shortname');
	$is_admin_sebrae = in_array('admin_sebrae', $shortnames);

	if (!$is_admin_revvo && !$is_admin_cliente && !$is_admin_sebrae) {
		return false;
	}

	return (object)[
		"name" => get_string('pluginname', 'local_catalog'),
		"icon" => 'icon-category',
		"url" => new \moodle_url("/local/catalog/index.php"),
		"active" => $PAGE->pagetype == "local-catalog" ? 'active' : '',
		"order" => 2
	];
}