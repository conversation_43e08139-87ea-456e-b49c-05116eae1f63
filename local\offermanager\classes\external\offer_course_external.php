<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_value;
use core_external\external_single_structure;
use core_external\external_multiple_structure;
use local_offermanager\persistent\offer_model;
use local_offermanager\persistent\offer_course_model;
use DateTime;
use core_date;
use moodle_exception;
use Exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_course_external
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_course_external extends external_api
{
    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function get_categories_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta', VALUE_DEFAULT, 0),
            'search_string' => new external_value(PARAM_TEXT, 'String para busca por nome de curso', VALUE_DEFAULT, ''),
        ]);
    }

    /**
     * Retorna a lista das categorias dos cursos adicionados à oferta.
     *
     * @param int $offerid ID da oferta.
     *
     * @return array
     * @throws moodle_exception
     */
    public static function get_categories($offerid, $search_string): array
    {
        self::validate_parameters(
            self::get_categories_parameters(),
            [
                'offerid' => $offerid,
                'search_string' => $search_string,
            ]
        );

        if (!$offerid) {
            return offer_course_model::get_all_categories($search_string);
        }

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        return $offer->get_categories($search_string);
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function get_categories_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID da categoria'),
                'name' => new external_value(PARAM_TEXT, 'Nome da categoria'),
            ]),
            'Lista das categorias dos cursos adicionados à oferta'
        );
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function fetch_current_courses_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta'),
            'categoryid' => new external_value(PARAM_INT, 'ID da categoria para realizar a busca', VALUE_DEFAULT, 0),
            'search_string' => new external_value(PARAM_TEXT, 'String para busca por nome de curso', VALUE_DEFAULT, ''),
            'exclude_courseids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do curso'),
                'Lista das dos cursos que devem ser excluídos dos resultados',
                VALUE_DEFAULT,
                []
            )

        ]);
    }

    /**
     * Busca nos cursos atuais da oferta os que atendem a busca.
     *
     * @param int $offerid ID da oferta.
     * @param int $categoryid ID da categoria.
     * @param array $courseids ID dos cursos.
     *
     * @return array
     * @throws moodle_exception
     */
    public static function fetch_current_courses(
        $offerid,
        $categoryid,
        $search_string,
        $exclude_courseids
    ): array {

        self::validate_parameters(
            self::fetch_current_courses_parameters(),
            compact('offerid', 'categoryid', 'search_string', 'exclude_courseids')
        );

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        return $offer->fetch_courses(
            $categoryid,
            $search_string,
            $exclude_courseids
        );
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function fetch_current_courses_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' =>  new external_value(PARAM_INT, 'Id do curso'),
                'fullname' => new external_value(PARAM_TEXT, 'Nome do curso'),
            ]),
            'Lista dos cursos encontrados na oferta, considerando os parâmetros de entrada'
        );
    }

    public static function get_current_courses_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta'),
            'only_active' => new external_value(PARAM_BOOL, 'Retorna apenas instâncias do curso que estão ativas', VALUE_DEFAULT, false),
            'courseids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do curso'),
                'Lista das dos cursos que devem ser mostrados nos resultados',
                VALUE_DEFAULT,
                []
            ),
            'page' => new external_value(PARAM_INT, 'Página', VALUE_DEFAULT, 1),
            'per_page' => new external_value(PARAM_INT, 'Registros por página', VALUE_DEFAULT, 5),
            'sort_by' => new external_value(PARAM_TEXT, 'Campo para ordenação', VALUE_DEFAULT, 'id'),
            'sort_direction' => new external_value(PARAM_TEXT, 'Ordem da ordenação', VALUE_DEFAULT, 'ASC'),
            'course_search' => new external_value(PARAM_TEXT, 'Termo de busca para filtrar cursos por nome', VALUE_DEFAULT, ''),
            'category_search' => new external_value(PARAM_TEXT, 'Termo de busca para filtrar cursos por categoria', VALUE_DEFAULT, '')
        ]);
    }

    /**
     * Retorna a lista de todos os cursos atuais da oferta.
     *
     * @param int $offerid ID da oferta.
     * @param bool $only_active Se deve retornar apenas cursos ativos.
     * @param array $courseids ID dos cursos.
     * @param int $page
     * @param int $per_page
     *
     * @return array
     * @throws moodle_exception
     */
    public static function get_current_courses(
        $offerid,
        $only_active,
        $courseids,
        $page,
        $per_page,
        $sort_by,
        $sort_direction,
        $course_search,
        $category_search
    ): array {

        self::validate_parameters(
            self::get_current_courses_parameters(),
            compact('offerid', 'only_active', 'courseids', 'page', 'per_page', 'sort_by', 'sort_direction', 'course_search', 'category_search')
        );

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $total_courses = count($offer->get_courses($only_active, $courseids, 0, 0, $sort_by, $sort_direction, $course_search, $category_search));
        $total_pages = $per_page > 0 ? ceil($total_courses / $per_page) : 1;

        $courses = array_map(
            function ($offer_course) {
                $course = $offer_course->get_course();
                $course_category = $offer_course->get_course_category();

                return [
                    'id' => $offer_course->get('id'),
                    'status' => $offer_course->get('status'),
                    'class_counter' => 0,
                    'categoryid' => $course_category->id,
                    'category_name' => $course_category->name,
                    'courseid' => $course->id,
                    'fullname' => $course->fullname,
                    'can_activate' => $offer_course->can_activate(),
                    'can_delete' => $offer_course->can_delete()
                ];
            },
            $offer->get_courses($only_active, $courseids, ($page - 1), $per_page, $sort_by, $sort_direction, $course_search, $category_search)
        );

        return [
            'page' => $page,
            'total_pages' => (int) $total_pages,
            'total_items' => (int) $total_courses,
            'courses' => $courses,
        ];
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function get_current_courses_returns(): external_single_structure
    {
        return new external_single_structure([
            'page' => new external_value(PARAM_INT, 'Página atual'),
            'total_pages' => new external_value(PARAM_INT, 'Número total de páginas'),
            'total_items' => new external_value(PARAM_INT, 'Número total de itens'),
            'courses' => new external_multiple_structure(
                new external_single_structure([
                    'id' => new external_value(PARAM_INT, 'ID do curso'),
                    'class_counter' => new external_value(PARAM_INT, 'Contador do número de turmas criadas no curso'),
                    'categoryid' => new external_value(PARAM_INT, 'Id da categoria do curso'),
                    'category_name' => new external_value(PARAM_TEXT, 'Nome da categoria do curso'),
                    'courseid' =>  new external_value(PARAM_INT, 'Id do curso'),
                    'fullname' => new external_value(PARAM_TEXT, 'Nome do curso'),
                    'status' => new external_value(PARAM_TEXT, 'Status do curso'),
                    'can_activate' => new external_value(PARAM_BOOL, 'Pode ativar o curso ?'),
                    'can_delete' => new external_value(PARAM_BOOL, 'Pode excluir o curso ?'),
                ]),
                'Lista de todos os cursos'
            ),
        ], 'Objeto contendo informações de paginação e a lista de cursos');
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function fetch_potential_courses_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta'),
            'categoryid' => new external_value(PARAM_INT, 'ID da categoria para filtragem', VALUE_DEFAULT, 0),
            'search_string' => new external_value(PARAM_TEXT, 'String para busca por nome de curso', VALUE_DEFAULT, ''),
            'exclude_courseids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do curso a ser excluído'),
                'Lista de IDs de cursos a serem excluídos da busca',
                VALUE_DEFAULT,
                []
            ),
            'page' => new external_value(PARAM_INT, 'Página', VALUE_DEFAULT, 1),
            'per_page' => new external_value(PARAM_INT, 'Registros por página', VALUE_DEFAULT, 5),
        ]);
    }

    /**
     * Retorna a lista dos cursos potenciais para serem adicionados na oferta.
     *
     * @param int $offerid ID da oferta.
     * @param int $categoryid ID da categoria.
     * @param string $search_string Texto para busca por nome de curso.
     * @param array $exclude_courseids Array com os courseids a excluir da busca.
     * 
     * @return object Objeto contendo a página atual, total de páginas e a lista de cursos.
     * @throws moodle_exception
     */
    public static function fetch_potential_courses($offerid, $categoryid, $search_string, $exclude_courseids, $page, $per_page): object
    {
        self::validate_parameters(
            self::fetch_potential_courses_parameters(),
            compact('offerid', 'categoryid', 'search_string', 'exclude_courseids', 'page', 'per_page')
        );

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $total_courses = $offer->count_potential_courses(
            $categoryid,
            $search_string,
            $exclude_courseids
        );

        $total_pages = $per_page > 0 ? ceil($total_courses / $per_page) : 1;

        $courses = $offer->fetch_potential_courses(
            $categoryid,
            $search_string,
            $exclude_courseids,
            ($page - 1),
            $per_page
        );

        return (object) [
            'page' => $page,
            'total_pages' => (int) $total_pages,
            'courses' => $courses,
        ];
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_single_structure
     */
    public static function fetch_potential_courses_returns(): external_single_structure
    {
        return new external_single_structure([
            'page' => new external_value(PARAM_INT, 'Página atual'),
            'total_pages' => new external_value(PARAM_INT, 'Número total de páginas'),
            'courses' => new external_multiple_structure(
                new external_single_structure([
                    'id' => new external_value(PARAM_INT, 'ID do curso'),
                    'fullname' => new external_value(PARAM_TEXT, 'Nome do curso'),
                ]),
                'Lista de cursos potenciais'
            ),
        ], 'Objeto contendo informações de paginação e a lista de cursos potenciais');
    }

    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function add_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerid' => new external_value(PARAM_INT, 'ID da oferta'),
            'courseids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do curso a ser adicionado'),
                'Lista de IDs de cursos a serem adicionados à oferta'
            ),
        ]);
    }

    /**
     * Cria a relação entre uma lista de cursos potenciais e uma oferta.
     *
     * @param int $offerid ID da oferta.
     * @param array $courseids Array com os courseids a adicionar.
     *
     * @return array Se a adição foi bem sucedida
     * @throws moodle_exception
     */
    public static function add($offerid, $courseids): array
    {
        self::validate_parameters(
            self::add_parameters(),
            compact('offerid', 'courseids')
        );

        $offer = offer_model::get_record(['id' => $offerid]);

        if (!$offer) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $results = [];

        foreach ($courseids as $courseid) {
            try {
                $offer_course = $offer->add_course($courseid);
                $results[] = [
                    'id' => $courseid,
                    'succeed' => !!$offer_course,
                ];
            } catch (Exception $e) {
                $results[] = [
                    'id' => $courseid,
                    'succeed' => false,
                ];
            }
        }

        return $results;
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function add_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do curso'),
                'succeed' => new external_value(PARAM_BOOL, 'Avalia se o curso foi adicionado'),
            ])
        );
    }
    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function get_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offercourseid' => new external_value(PARAM_INT, 'ID da Relação Curso-Oferta'),
        ]);
    }

    /**
     * Retorna o cursos adicionados à oferta.
     *
     * @param int $offercourseid ID da relação entre oferta e curso.
     *
     * @return array
     * @throws moodle_exception
     */
    public static function get($offercourseid): array
    {
        self::validate_parameters(
            self::delete_parameters(),
            [
                'offercourseid' => $offercourseid
            ]
        );

        $offer_course = offer_course_model::get_record(['id' => $offercourseid]);

        if (!$offer_course) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $course = $offer_course->get_course();

        $datetime = new DateTime('now', core_date::get_user_timezone_object());

        $startdate = $enddate = null;

        if ($course->startdate) {
            $datetime->setTimestamp($course->startdate);
            $startdate = $datetime->format('Y-m-d');
        }

        if ($course->enddate) {
            $datetime->setTimestamp($course->enddate);
            $enddate = $datetime->format('Y-m-d');
        }

        return [
            'id' => $offer_course->get('id'),
            'offerid' => $offer_course->get('offerid'),
            'courseid' => $course->id,
            'startdate' => $startdate,
            'enddate' => $enddate
        ];
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_single_structure
     */
    public static function get_returns(): external_single_structure
    {
        return new external_single_structure([
            'id' => new external_value(PARAM_INT, 'offercourseid'),
            'offerid' => new external_value(PARAM_INT, 'offerid'),
            'courseid' => new external_value(PARAM_INT, 'courseid'),
            'startdate' => new external_value(PARAM_TEXT, 'data de início do curso em formato yyyy-mm-dd'),
            'enddate' => new external_value(PARAM_TEXT, 'data fim do curso em formato yyyy-mm-dd'),
        ]);
    }
    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function delete_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offercourseid' => new external_value(PARAM_INT, 'ID da Relação Curso-Oferta'),
        ]);
    }

    /**
     * Retorna a lista das categorias dos cursos adicionados à oferta.
     *
     * @param int $offercourseid ID da relação entre oferta e curso.
     *
     * @return bool
     * @throws moodle_exception
     */
    public static function delete($offercourseid): bool
    {
        self::validate_parameters(
            self::delete_parameters(),
            [
                'offercourseid' => $offercourseid
            ]
        );

        $offer_course = offer_course_model::get_record(['id' => $offercourseid]);

        if (!$offer_course) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        return $offer_course->delete();
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_value
     */
    public static function delete_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Confirma a exclusão.');
    }

    /**
     * Define os parâmetros para alterar o status de uma oferta.
     *
     * @return external_function_parameters
     */
    public static function set_status_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID da relação curso-oferta a ser alterada'),
            'status' => new external_value(PARAM_BOOL, 'Status a ser alterado. 0 para desabilitar e 1 para habilitar')
        ]);
    }

    /**
     * Alterar o status de uma relação curso-oferta.
     *
     * @param int $id
     * @param bool $status
     * @return array
     * @throws moodle_exception
     */
    public static function set_status($id, $status)
    {
        self::validate_parameters(
            self::set_status_parameters(),
            compact('id', 'status')
        );

        $offer_course = offer_course_model::get_record(
            [
                'id' => $id
            ]
        );

        if (!$offer_course) {
            throw new moodle_exception('error:offer_not_found', 'local_offermanager');
        }

        $current_status = (bool) $offer_course->get('status');

        if ($status == $current_status) {

            $message_key = $status
                ? 'error:offer_course_already_active'
                : 'error:offer_course_already_inactive';

            throw new moodle_exception($message_key, 'local_offermanager');
        }

        $status
            ? $offer_course->activate()
            : $offer_course->inactivate();

        $message_key = $status
            ? 'event:offercourseactivated'
            : 'event:offercourseinactivated';

        return [
            'status' => $offer_course->get('status'),
            'message' => get_string($message_key, 'local_offermanager'),
        ];
    }

    /**
     * Define a estrutura de retorno da função set_status.
     *
     * @return external_single_structure
     */
    public static function set_status_returns()
    {
        return new external_single_structure([
            'status' => new external_value(PARAM_BOOL, 'O status atual'),
            'message' => new external_value(PARAM_TEXT, 'Mensagem de retorno'),
        ]);
    }
    /**
     * Define os parâmetros do método.
     *
     * @return external_function_parameters
     */
    public static function get_classes_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offercourseid' => new external_value(PARAM_INT, 'ID da relação entre oferta e curso'),
        ]);
    }

    /**
     * Retorna as turmas de um curso específico de uma oferta.
     *
     * @param int $offercourseid ID da relação entre oferta e curso.
     *
     * @return array
     * @throws moodle_exception
     */
    public static function get_classes($offercourseid): array
    {
        self::validate_parameters(
            self::get_classes_parameters(),
            [
                'offercourseid' => $offercourseid
            ]
        );

        $offer_course = offer_course_model::get_record(['id' => $offercourseid]);

        if (!$offer_course) {
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager');
        }

        $classes = $offer_course->get_classes();
        $result = [];

        foreach ($classes as $class) {
            $enrol_instance = $class->get_enrol_instance();

            $enrolled_users_count = count($class->get_user_enrolments());

            $max_users = $class->get_mapped_field('maxusers') ?? 0;
            $result[] = [
                'id' => $class->get('id'),
                'name' => $class->get_mapped_field('classname'),
                'enrol' => $enrol_instance->enrol,
                'enrolled_users' => $enrolled_users_count,
                'max_users' => $max_users,
                'startdate' => $class->get_mapped_field('startdate'),
                'enddate' => $class->get_mapped_field('enddate'),
                'status' => $class->get('status'),
                'can_activate' => $class->can_activate(),
                'can_delete' => $class->can_delete(),
                'offerid' => $offer_course->get('offerid')
            ];
        }

        return $result;
    }

    /**
     * Define a estrutura de retorno do método.
     *
     * @return external_multiple_structure
     */
    public static function get_classes_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID da turma'),
                'name' => new external_value(PARAM_TEXT, 'Nome da turma'),
                'enrol' => new external_value(PARAM_TEXT, 'Tipo de inscrição'),
                'enrolled_users' => new external_value(PARAM_INT, 'Número de usuários inscritos'),
                'max_users' => new external_value(PARAM_INT, 'Número máximo de vagas'),
                'startdate' => new external_value(PARAM_INT, 'Data de início da turma'),
                'enddate' => new external_value(PARAM_INT, 'Data de fim da turma'),
                'status' => new external_value(PARAM_INT, 'Status da turma'),
                'can_activate' => new external_value(PARAM_BOOL, 'Pode ativar/desativar a turma?'),
                'can_delete' => new external_value(PARAM_BOOL, 'Pode excluir a turma?'),
                'offerid' => new external_value(PARAM_INT, 'ID da oferta'),
            ]),
            'Lista de turmas do curso'
        );
    }

    /**
     * Define os parâmetros do método get_course_roles.
     *
     * @return external_function_parameters
     */
    public static function get_course_roles_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offercourseid' => new external_value(PARAM_INT, 'ID da relação entre oferta e curso'),
        ]);
    }

    /**
     * Retorna a lista de papéis disponíveis no curso associado à relação oferta-curso.
     *
     * @param int $offercourseid ID da relação entre oferta e curso.
     *
     * @return array
     * @throws moodle_exception
     */
    public static function get_course_roles(int $offercourseid): array
    {
        self::validate_parameters(
            self::get_course_roles_parameters(),
            ['offercourseid' => $offercourseid]
        );

        $offer_course = offer_course_model::get_record(['id' => $offercourseid]);

        if (!$offer_course) {
            throw new moodle_exception('error:offer_course_not_found', 'local_offermanager');
        }

        $roles = $offer_course->get_course_roles();

        $result = [];

        foreach ($roles as $id => $name) {
            $result[] = ['id' => $id, 'name' => $name];
        }

        return $result;
    }

    /**
     * Define a estrutura de retorno do método get_course_roles.
     *
     * @return external_multiple_structure
     */
    public static function get_course_roles_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'ID do papel'),
                    'name' => new external_value(PARAM_TEXT, 'Nome do papel'),
                ]
            ),
            'Lista de papéis disponíveis no curso.'
        );
    }
}
